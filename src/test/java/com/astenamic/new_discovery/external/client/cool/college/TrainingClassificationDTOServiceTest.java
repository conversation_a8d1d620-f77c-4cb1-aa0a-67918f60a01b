package com.astenamic.new_discovery.external.client.cool.college;

import com.astenamic.new_discovery.external.client.cool.college.service.TrainingClassificationApiService;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.TrainingClassificationDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.TrainingClassificationQuery;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class TrainingClassificationDTOServiceTest {

    @Autowired
    private TrainingClassificationApiService trainingClassificationApiService;

    @Test
    public void testDummy() {
        // This is a placeholder test method.
        List<TrainingClassificationDTO> list = trainingClassificationApiService.list(TrainingClassificationQuery.builder().build());
        System.out.println("培训分类树节点数：" + (list == null ? 0 : list.size()));
    }
}
