package com.astenamic.new_discovery.external.client.cool.college.service;

import com.astenamic.new_discovery.external.modal.dto.cool.college.study.StudyProjectInfoDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.StudyProjectsQuery;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * 学习项目查询服务单元测试
 */
@SpringBootTest
public class StudyProjectApiServiceTest {

    @Autowired
    private StudyProjectApiService studyProjectApiService;

    @Test
    public void testList() {

        StudyProjectsQuery query = StudyProjectsQuery.builder()
                .build();

        // 查询
        List<StudyProjectInfoDTO> pageResult = studyProjectApiService.listAll(query);

        // 输出结果
        System.out.println("学习项目数：" + pageResult.size());

    }
}
