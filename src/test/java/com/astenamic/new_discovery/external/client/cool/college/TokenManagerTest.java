package com.astenamic.new_discovery.external.client.cool.college;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class TokenManagerTest {

    @Autowired
    private TokenManager tokenManager;

    @Test
    public void testGetToken() {
        String token = tokenManager.getToken();
        System.out.println("Retrieved token: " + token);
    }
}
