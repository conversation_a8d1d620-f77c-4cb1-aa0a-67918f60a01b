package com.astenamic.new_discovery.external.client.cool.college;

import com.astenamic.new_discovery.external.client.cool.college.service.StudyProjectMonitorApiService;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.StudyProjectMonitorDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.StudyProjectMonitorQuery;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class StudyProjectMonitorApiServiceTest {

    @Autowired
    private StudyProjectMonitorApiService monitorService;

    @Test
    public void testMonitor() {

        StudyProjectMonitorQuery query = StudyProjectMonitorQuery.builder()
                .pageNumber(1)
                .pageSize(50)
                .projectCourseId(2207047325061353472L)
                .build();

        StudyProjectMonitorDTO data = monitorService.queryAll(query);

        System.out.println("学员数量：" + data.getTotalCount());

        data.getMonitorList().getList().forEach(System.out::println);

    }
}
