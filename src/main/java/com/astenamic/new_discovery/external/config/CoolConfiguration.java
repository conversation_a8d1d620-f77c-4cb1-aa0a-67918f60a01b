package com.astenamic.new_discovery.external.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "cool")
public class CoolConfiguration {


    private Vision vision;

    private College college;

    @Data
    public static class Vision {

        private String baseUrl;

        private String privateKey;

        private String enterpriseId;

    }

    @Data
    public static class College {

        private String baseUrl;

        private String apiKey;

        private String apiSecret;

        private String enterpriseId;

        private int connectTimeoutMs = 3000;

        private int readTimeoutMs    = 5000;

    }
}
