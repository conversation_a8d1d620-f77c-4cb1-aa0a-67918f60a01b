package com.astenamic.new_discovery.external.client.cool.college;

import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.cool.college.CoolCollegeException;
import com.astenamic.new_discovery.external.modal.dto.cool.college.CoolCollegeResponse;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.ResolvableType;
import org.springframework.http.*;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.*;
import org.springframework.web.util.UriBuilder;
import reactor.netty.http.client.HttpClient;
import io.netty.channel.ChannelOption;

import java.time.Duration;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class CoolCollegeApiClient {

    private final TokenManager tokenManager;
    private final CoolConfiguration globalCfg;

    private WebClient web;
    private CoolConfiguration.College cfg;

    @PostConstruct
    void init() {
        this.cfg = globalCfg.getCollege();

        ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 5MB
                .build();

        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, cfg.getConnectTimeoutMs())
                .responseTimeout(Duration.ofMillis(cfg.getReadTimeoutMs()));

        this.web = WebClient.builder()
                .baseUrl(cfg.getBaseUrl())         
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .exchangeStrategies(strategies)
                .build();
    }

    /* ========= 对外方法 ========= */
    public <T> T get(String path, Map<String, ?> query, Class<?>... dataLayers) {
        return call(HttpMethod.GET, path, query, null, dataLayers);
    }

    public <T> T post(String path, Object body, Class<?>... dataLayers) {
        return call(HttpMethod.POST, path, null, body, dataLayers);
    }

    public <T> T post(String path, Map<String, ?> query, Object body, Class<?>... dataLayers) {
        return call(HttpMethod.POST, path, query, body, dataLayers);
    }

    /* ========= 核心实现 ========= */
    private <T> T call(HttpMethod method,
                       String path,
                       Map<String, ?> query,
                       Object body,
                       Class<?>... dataLayers) {

        if (dataLayers == null || dataLayers.length == 0) {
            throw new IllegalArgumentException("必须至少传入一个 data Class");
        }

        ParameterizedTypeReference<CoolCollegeResponse<T>> respRef = ParameterizedTypeReference.forType(
                ResolvableType.forClassWithGenerics(
                        CoolCollegeResponse.class,
                        buildType(0, dataLayers)
                ).getType());

        // 构造请求
        WebClient.RequestBodyUriSpec uriSpec = web.method(method);

        WebClient.RequestHeadersSpec<?> headersSpec = uriSpec.uri(uriBuilder -> {
                    UriBuilder b = uriBuilder.path(path);
                    if (query != null) {
                        query.forEach(b::queryParam);
                    }
                    return b.build();
                })
                .header("o-access-token", tokenManager.getToken())
                .accept(MediaType.APPLICATION_JSON);

        if (body != null
                && (method == HttpMethod.POST || method == HttpMethod.PUT || method == HttpMethod.PATCH)) {
            headersSpec = ((WebClient.RequestBodySpec) headersSpec)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(body);
        }


        CoolCollegeResponse<T> resp = headersSpec.retrieve()
                .onStatus(HttpStatusCode::is4xxClientError,
                        r -> r.bodyToMono(String.class).map(CoolCollegeException::clientError))
                .onStatus(HttpStatusCode::is5xxServerError,
                        r -> r.bodyToMono(String.class).map(CoolCollegeException::serverError))
                .bodyToMono(respRef)
                .block();

        if (resp == null || !resp.isSuccess()) {
            throw new CoolCollegeException(
                    500, resp == null ? "null response" : resp.getMessage());
        }
        return resp.getData();
    }

    /* ========= 工具：递归构造嵌套类型 ========= */
    private static ResolvableType buildType(int idx, Class<?>[] arr) {
        if (idx == arr.length - 1) {
            return ResolvableType.forClass(arr[idx]);
        }
        return ResolvableType.forClassWithGenerics(arr[idx],
                buildType(idx + 1, arr));
    }
}
