package com.astenamic.new_discovery.external.client.cool.college;

import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.cool.college.CoolCollegeResponse;
import lombok.Data;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.time.Instant;
import java.util.Base64;
import java.util.Map;

/**
 * 调用 /v2/authenticate 换取 AccessToken
 */
@Component
public class CoolCollegeAuthClient {

    private final CoolConfiguration.College cfg;
    private final WebClient webClient;

    public CoolCollegeAuthClient(CoolConfiguration globalCfg) {
        this.cfg = globalCfg.getCollege();
        this.webClient = WebClient.builder()
                .baseUrl(cfg.getBaseUrl())
                .build();
    }

    public AccessTokenData authenticate() {
        long ts = Instant.now().getEpochSecond();
        Map<String, Object> body = Map.of(
                "signature", sign(cfg.getApiKey(), cfg.getApiSecret(), ts),
                "api_key", cfg.getApiKey(),
                "api_secret", cfg.getApiSecret(),
                "timestamp", ts);

        ParameterizedTypeReference<CoolCollegeResponse<AccessTokenData>> ref =
                new ParameterizedTypeReference<>() {
                };
        CoolCollegeResponse<AccessTokenData> resp = webClient.post()
                .uri("/v2/authenticate")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(body)
                .retrieve()
                .bodyToMono(ref)
                .block();

        if (resp == null || !resp.isSuccess()) {
            throw new IllegalStateException("authenticate failed: " +
                    (resp == null ? "null response" : resp.getMessage()));
        }
        return resp.getData();
    }

    /**
     * 生成签名：HmacSHA256( timestamp + "\n" + apiKey , apiSecret )
     */
    private String sign(String apiKey, String apiSecret, long timestamp) {
        try {
            String payload = timestamp + "\n" + apiKey;   // 正确的拼接方式
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(apiSecret.getBytes(), "HmacSHA256"));
            byte[] raw = mac.doFinal(payload.getBytes());
            return Base64.getEncoder().encodeToString(raw);
        } catch (Exception e) {
            throw new RuntimeException("sign error", e);
        }
    }


    /* ---------------- DTO ---------------- */
    @Data
    public static class AccessTokenData {
        private String access_token;
        private long expire_at;
    }
}
