package com.astenamic.new_discovery.external.client.cool.college.service;

import com.astenamic.new_discovery.external.client.cool.college.CoolCollegeApiClient;
import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.cool.college.CoolCollegePageResult;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.StudyProjectMonitorDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.StudyProjectMonitorQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 学习项目监控查询服务
 */
@Service
@RequiredArgsConstructor
public class StudyProjectMonitorApiService {

    private final CoolCollegeApiClient api;
    private final CoolConfiguration cfg;

    /**
     * 拼接接口路径 /api/v2/{enterpriseId}/study_projects/{courseId}/user_monitors
     */
    private String url(long courseId) {
        return String.format("/api/v2/%s/study_projects/%d/user_monitors",
                cfg.getCollege().getEnterpriseId(), courseId);
    }


    /**
     * 查询全部学习项目监控学员详情
     *
     * @param q 查询参数
     * @return 学员详情全量列表
     */
    public StudyProjectMonitorDTO queryAll(StudyProjectMonitorQuery q) {

        int page = 1;
        int pageSize = q.getPageSize() > 0 ? q.getPageSize() : 50;

        StudyProjectMonitorDTO result = null;
        List<StudyProjectMonitorDTO.MonitorStudentInfo> all = new ArrayList<>();

        boolean hasMore;

        do {
            q.setPageNumber(page);
            q.setPageSize(pageSize);

            StudyProjectMonitorDTO pageData = query(q);

            if (result == null) {
                result = pageData;                           // 保存第一页的统计字段 / 项目信息
            }

            var list = pageData.getMonitorList() == null
                    ? Collections.<StudyProjectMonitorDTO.MonitorStudentInfo>emptyList()
                    : pageData.getMonitorList().getList();

            all.addAll(list);

            hasMore = list.size() == pageSize;                      // 这一页不足 pageSize → 已经最后一页
            page++;
        } while (hasMore);

        CoolCollegePageResult<StudyProjectMonitorDTO.MonitorStudentInfo> mergedPage = new CoolCollegePageResult<>();
        mergedPage.setList(all);
        mergedPage.setTotal(all.size());
        result.setMonitorList(mergedPage);

        return result;
    }


    /**
     * 查询学习项目监控
     */
    public StudyProjectMonitorDTO query(StudyProjectMonitorQuery q) {

        Map<String, Object> params = new LinkedHashMap<>();
        params.put("page_number", q.getPageNumber());
        params.put("page_size", q.getPageSize());
        params.put("project_course_id", q.getProjectCourseId());

        if (q.getDepartmentId() != null) params.put("department_id", q.getDepartmentId());
        if (q.getKeyword() != null) params.put("keyword", q.getKeyword());
        if (q.getOrderBy() != null) params.put("order_by", q.getOrderBy());
        if (q.getQualifiedStatus() != null) params.put("qualified_status", q.getQualifiedStatus());
        if (q.getSort() != null) params.put("sort", q.getSort());
        if (q.getStudyStatus() != null) params.put("study_status", q.getStudyStatus());
        if (q.getStudyType() != null) params.put("study_type", q.getStudyType());
        if (q.getTaskBeginTime() != null) params.put("task_begin_time", q.getTaskBeginTime());
        if (q.getTaskEndTime() != null) params.put("task_end_time", q.getTaskEndTime());

        return api.get(url(q.getProjectCourseId()), params, StudyProjectMonitorDTO.class);
    }
}
