package com.astenamic.new_discovery.external.client.cool.college;


import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

import java.time.Instant;

/**
 * 本地缓存 token，提前 60 s 自动刷新
 */
@Component
@RequiredArgsConstructor
public class TokenManager {

    private final CoolCollegeAuthClient authClient;
    private volatile String token;
    private volatile long expireAt;

    public synchronized String getToken() {
        long now = Instant.now().getEpochSecond();
        if (token == null || now >= expireAt - 60) {
            var d = authClient.authenticate();
            token = d.getAccess_token();
            expireAt = d.getExpire_at();
        }
        return token;
    }
}
