package com.astenamic.new_discovery.external.client.cool.college.service;

import com.astenamic.new_discovery.external.client.cool.college.CoolCollegeApiClient;
import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.TrainingClassificationDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.TrainingClassificationQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 学习项目培训分类查询服务
 */
@Service
@RequiredArgsConstructor
public class TrainingClassificationApiService {

    private final CoolCollegeApiClient api;

    private final CoolConfiguration cfg;

    /**
     * 接口路径 /api/v2/{enterpriseId}/classification/tree
     */
    private String url() {
        return String.format("/api/v2/%s/classification/tree",
                cfg.getCollege().getEnterpriseId());
    }

    /**
     * 查询培训分类树
     *
     * @param q 查询条件
     * @return 分类树（List 根节点）
     */
    public List<TrainingClassificationDTO> list(TrainingClassificationQuery q) {

        Map<String, Object> params = new LinkedHashMap<>();

        if (q.getEnabled() != null) params.put("enabled", q.getEnabled());
        if (q.getKeyword() != null) params.put("keyword", q.getKeyword());
        if (q.getLevelLimit() != null) params.put("level_limit", q.getLevelLimit());
        if (q.getShowAllFields() != null) params.put("show_all_fields", q.getShowAllFields());

        return api.post(url(), params, "{}", List.class, TrainingClassificationDTO.class);
    }
}
