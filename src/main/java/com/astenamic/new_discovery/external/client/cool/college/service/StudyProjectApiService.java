package com.astenamic.new_discovery.external.client.cool.college.service;


import com.astenamic.new_discovery.external.client.cool.college.CoolCollegeApiClient;
import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.cool.college.CoolCollegePageResult;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 学习项目查询服务
 */
@Service
@RequiredArgsConstructor
public class StudyProjectApiService {

    private final CoolCollegeApiClient api;

    private final CoolConfiguration cfg;

    private String url() {
        return String.format("/api/v2/%s/study_projects_fast", cfg.getCollege().getEnterpriseId());
    }

    /**
     * 查询所有学习项目
     *
     * @param q 查询参数
     * @return 所有学习项目列表
     */
    public List<StudyProjectInfoDTO> listAll(StudyProjectsQuery q) {
        int pageSize = 100; // 可以自定义
        int pageNum = 1;
        List<StudyProjectInfoDTO> all = new ArrayList<>();
        List<StudyProjectInfoDTO> currentPageList;

        do {
            StudyProjectsQuery pageQuery = StudyProjectsQuery.builder()
                    .pageNumber(pageNum)
                    .pageSize(pageSize)
                    .beginTime(q.getBeginTime())
                    .endTime(q.getEndTime())
                    .classifyId(q.getClassifyId())
                    .departmentId(q.getDepartmentId())
                    .keyword(q.getKeyword())
                    .status(q.getStatus())
                    .timeType(q.getTimeType())
                    .build();

            CoolCollegePageResult<StudyProjectInfoDTO> page = this.list(pageQuery);
            currentPageList = page.getList();
            if (currentPageList != null && !currentPageList.isEmpty()) {
                all.addAll(currentPageList);
            }
            pageNum++;
        } while (currentPageList != null && currentPageList.size() == pageSize);

        return all;
    }


    /**
     * 查询学习项目列表
     *
     * @param q 查询参数
     * @return 分页的学习项目列表
     */
    public CoolCollegePageResult<StudyProjectInfoDTO> list(StudyProjectsQuery q) {

        Map<String, Object> params = new LinkedHashMap<>();
        params.put("page_number", q.getPageNumber());
        params.put("page_size", q.getPageSize());

        if (q.getBeginTime() != null) params.put("begin_time", q.getBeginTime());
        if (q.getEndTime() != null) params.put("end_time", q.getEndTime());
        if (q.getClassifyId() != null) params.put("classify_id", q.getClassifyId());
        if (q.getDepartmentId() != null) params.put("department_id", q.getDepartmentId());
        if (q.getKeyword() != null) params.put("keyword", q.getKeyword());
        if (q.getStatus() != null) params.put("status", q.getStatus());
        if (q.getTimeType() != null) params.put("time_type", q.getTimeType());

        return api.get(url(), params,
                CoolCollegePageResult.class,
                StudyProjectInfoDTO.class);
    }
}

