package com.astenamic.new_discovery.external.modal.dto.cool.college;


import lombok.Data;

import java.util.List;

/**
 * 酷学院通用分页结果包装对象
 *
 * @param <T> 实际数据类型
 */
@Data
public class CoolCollegePageResult<T> {


    /**
     * 是否有下一页
     */
    private String hasNextPage;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 总记录数
     */
    private Integer total;

    /**
     * 每页显示条数
     */
    private Integer pageSize;

    /**
     * 当前页数据列表
     */
    private List<T> list;


}