package com.astenamic.new_discovery.external.modal.dto.cool.college.study;


import lombok.Builder;
import lombok.Data;

/**
 * 学习项目培训分类查询参数
 */
@Data
@Builder
public class TrainingClassificationQuery {

    /**
     * 是否仅查询启用分类（可选）
     */
    private Boolean enabled;

    /**
     * 搜索关键字（可选）
     */
    private String keyword;

    /**
     * 限制层级（仅查询上级部门时生效，可选）
     */
    private Integer levelLimit;

    /**
     * 是否返回所有字段（列表页需传 true，可选）
     */
    private Boolean showAllFields;
}

