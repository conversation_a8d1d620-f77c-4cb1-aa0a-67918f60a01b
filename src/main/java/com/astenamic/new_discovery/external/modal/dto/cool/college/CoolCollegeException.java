package com.astenamic.new_discovery.external.modal.dto.cool.college;

/**
 * 包装 4xx / 5xx 错误
 */
public class CoolCollegeException extends RuntimeException {

    private final int statusCode;

    public CoolCollegeException(int code, String msg) {
        super(msg);
        this.statusCode = code;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public static CoolCollegeException clientError(String b) {
        return new CoolCollegeException(400, b);
    }

    public static CoolCollegeException serverError(String b) {
        return new CoolCollegeException(500, b);
    }
}
