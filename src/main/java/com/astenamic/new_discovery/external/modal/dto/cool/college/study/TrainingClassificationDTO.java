package com.astenamic.new_discovery.external.modal.dto.cool.college.study;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 学习项目培训分类
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TrainingClassificationDTO {

    /**
     * 分类路径（不含自身，逗号分隔）
     */
    private String parentIdChain;

    /**
     * 创建时间（毫秒时间戳）
     */
    private Long createTime;

    /**
     * 级别：1→一级 2→二级 3→三级
     */
    private Integer level;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 类型名称
     */
    private String title;

    /**
     * 类型：0 系统预制 1 业务
     */
    private Integer type;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 父节点是否启用
     */
    private Boolean parentEnable;

    /**
     * 更新时间（毫秒时间戳）
     */
    private Long updateTime;

    /**
     * 更新人 ID
     */
    private Long updateUser;

    /**
     * 子节点列表
     */
    private List<TrainingClassificationDTO> children;

    /**
     * 父级 ID（无父节点为 0）
     */
    private Long parentId;

    /**
     * 分类名
     */
    private String name;

    /**
     * 是否禁用
     */
    private Boolean disabled;

    /**
     * 分类 ID
     */
    private Long id;

    /**
     * 创建人 ID
     */
    private Long createUser;

    /**
     * 分类 key（与 id 一致）
     */
    private Long key;
}
