package com.astenamic.new_discovery.external.modal.dto.cool.college.study;

import lombok.Builder;
import lombok.Data;

/**
 * 查询学习项目列表参数
 */
@Data
@Builder
public class StudyProjectsQuery {

    /**
     * 当前页码, 示例值(1)
     */
    private int pageNumber;

    /**
     * 每页数据容量, 示例值(10)
     */
    private int pageSize;

    /**
     * 开始时间, 示例值(2023-04-17)
     */
    private String beginTime;

    /**
     * 结束时间, 示例值(2023-04-30)
     */
    private String endTime;

    /**
     * 资源分类ID
     */
    private Long classifyId;

    /**
     * 组织部门ID
     */
    private String departmentId;

    /**
     * 搜索关键词
     */
    private String keyword;

    /**
     * 任务状态：normal-已发布，disabled-未发布，draft-草稿
     */
    private String status;

    /**
     * 事件类型：createTime-创建时间，updateTime-更新时间
     */
    private String timeType;
}
