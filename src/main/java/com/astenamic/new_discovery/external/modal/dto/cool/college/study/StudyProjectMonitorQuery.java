package com.astenamic.new_discovery.external.modal.dto.cool.college.study;

import lombok.Builder;
import lombok.Data;

/**
 * 查询学习项目监控列表参数
 */
@Data
@Builder
public class StudyProjectMonitorQuery {

    /**
     * 当前页码（从 1 开始）
     */
    private int pageNumber;

    /**
     * 每页条数
     */
    private int pageSize;

    /**
     * 学习项目 courseId（必填）
     */
    private Long projectCourseId;

    /**
     * 部门 ID（可选）
     */
    private Long departmentId;

    /**
     * 搜索关键词（可选）
     */
    private String keyword;

    /**
     * 排序字段（process）
     */
    private String orderBy;

    /**
     * 合格状态 true / false
     */
    private String qualifiedStatus;

    /**
     * asc / desc
     */
    private String sort;

    /**
     * 学习状态 unstarted/finished/unfinished/overdue
     */
    private String studyStatus;

    /**
     * 学习类型 task / self_study
     */
    private String studyType;

    /**
     * 任务开始时间（字符串 yyyy-MM-dd）
     */
    private String taskBeginTime;

    /**
     * 任务结束时间（字符串 yyyy-MM-dd）
     */
    private String taskEndTime;
}

