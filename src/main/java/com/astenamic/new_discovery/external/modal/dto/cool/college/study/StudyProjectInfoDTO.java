package com.astenamic.new_discovery.external.modal.dto.cool.college.study;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 学习项目基础信息
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class StudyProjectInfoDTO {

    /**
     * 学习项目ID
     */
    private String id;

    /**
     * 学习项目名称
     */
    private String title;

    /**
     * 项目类型（如：study_project-学习项目，camp_period-营期项目等）
     */
    private String type;

    /**
     * 项目分数
     */
    private Integer score;

    /**
     * 阶段任务数
     */
    private Integer stageCcourseNum;

    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 来源类型（如：pc-电脑端，app-移动端等）
     */
    private String sourceType;

    /**
     * 线下培训时长
     */
    private Integer offlineTrainingTime;

    /**
     * 作业总次数
     */
    private Integer totalOperationCount;

    /**
     * 项目状态（normal-已发布，disabled-未发布，draft-草稿）
     */
    private String status;

    /**
     * 是否获得学分（字符串类型，"true"/"false"）
     */
    private String isGetScoreStr;

    /**
     * 学习模式（如：free_mode-自由模式，unlock_mode-闯关模式）
     */
    private String studyModel;

    /**
     * 文档学习时长
     */
    private Integer studyTimeLimit;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 学习进度
     */
    private Integer progress;

    /**
     * 任务类型（如course/课程，study_exam/学考等）
     */
    private String taskType;

    /**
     * 学时估算（单位：分钟）
     */
    private Integer studyDuration;

    /**
     * 资源项数
     */
    private Integer resourceCount;

    /**
     * 分类ID（后端返回字段名为 classify）
     */
    private String classify;

    /**
     * 证书ID
     */
    private String certificateId;

    /**
     * 完成人数
     */
    private Integer finishUserCount;

    /**
     * 项目课程ID
     */
    private String courseId;

    /**
     * 总培训时长
     */
    private Integer totalTrainingTime;

    /**
     * 学习项目分类ID
     */
    private String trainingClassificationId;
}
