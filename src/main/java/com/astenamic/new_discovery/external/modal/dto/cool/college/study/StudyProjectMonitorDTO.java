package com.astenamic.new_discovery.external.modal.dto.cool.college.study;

import com.astenamic.new_discovery.external.modal.dto.cool.college.CoolCollegePageResult;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;


/**
 * 查询学习项目监控接口 —— 顶层返回体
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class StudyProjectMonitorDTO {

    /**
     * 已完成学员数量
     */
    private Integer finishedCount;

    /**
     * 学员总数量
     */
    private Integer totalCount;

    /**
     * 逾期学员数量
     */
    private Integer overdueCount;

    /**
     * 未完成学员数量
     */
    private Integer unfinishedCount;

    /**
     * 学习项目基本信息
     */
    private StudyProjectInfoDTO studyProject;

    /**
     * 统计信息（参与率 / 完成率 / 合格率）
     */
    private Summarizing summarizing;

    /**
     * 分页的学员监控列表
     */
    private CoolCollegePageResult<MonitorStudentInfo> monitorList;

    /* ───────────────── 内部数据结构 ───────────────── */

    /**
     * 三大统计块：参与、完成、合格
     */
    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Summarizing {

        /**
         * 参与统计
         */
        private RatioVo partSummarizingVo;

        /**
         * 完成统计（包含进行中 / 逾期 / 完成）
         */
        private FinishVo finishSummarizingVo;

        /**
         * 合格统计
         */
        private RatioVo qualifiedSummarizingVo;
    }

    /**
     * 通用比率对象
     */
    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class RatioVo {

        /**
         * 实际发生数量，例如 actual_part_num / qualified_num
         */
        private Integer actualPartNum;

        /**
         * 应有数量，例如 should_part_num / total_num
         */
        private Integer shouldPartNum;

        /**
         * 比率（0.0 ~ 1.0）
         */
        private Double ratio;
    }

    /**
     * 完成统计（进行中 / 逾期 / 已完成）
     */
    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class FinishVo {

        /**
         * 进行中数量
         */
        private Integer inHandNum;

        /**
         * 逾期数量
         */
        private Integer overdueNum;

        /**
         * 已完成数量
         */
        private Integer finishNum;

        /**
         * 完成率（0.0 ~ 1.0）
         */
        private Double ratio;
    }

    /**
     * 学员监控核心信息（分页 list 元素）
     */
    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class MonitorStudentInfo {

        /**
         * 用户 ID
         */
        private Long userId;

        /**
         * 用户姓名
         */
        private String userName;

        /**
         * 部门名称
         */
        private String departmentName;

        /**
         * 项目状态：unstarted / unfinished / finished / overdue
         */
        private String projectStatus;

        /**
         * 总进度（百分比）
         */
        private Integer process;

        /**
         * 学习完成进度（百分比）
         */
        private Integer studyProgress;

        /**
         * 是否合格
         */
        private Boolean qualifiedStatus;

        /**
         * 合格状态文字
         */
        private String qualifiedStatusName;

        /**
         * 关联考试数量
         */
        private Integer examCount;

        /**
         * 已参加考试场次
         */
        private Integer examJoinCount;

        /**
         * 及格考试场次
         */
        private Integer examPassCount;

        /**
         * 最新学习时间（yyyy-MM-dd HH:mm:ss）
         */
        private String updateTime;

        /**
         * 任务开始时间
         */
        private String taskBeginTime;

        /**
         * 任务结束时间
         */
        private String taskEndTime;
    }
}

