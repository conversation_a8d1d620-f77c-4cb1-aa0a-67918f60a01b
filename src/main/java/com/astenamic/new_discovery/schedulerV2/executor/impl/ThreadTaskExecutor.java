package com.astenamic.new_discovery.schedulerV2.executor.impl;

import com.astenamic.new_discovery.schedulerV2.alert.TaskAlertService;
import com.astenamic.new_discovery.schedulerV2.executor.TaskExecutor;
import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;
import com.astenamic.new_discovery.common.util.ThreadPoolUtils;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@AllArgsConstructor
public class ThreadTaskExecutor implements TaskExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ThreadTaskExecutor.class);

    private static final String EXECUTOR_TYPE = "thread";

    private final TaskAlertService alertService;


    @Override
    public void execute(TaskDefinition taskDefinition, Runnable task) {
        logger.info("使用线程池执行器执行任务: {}", taskDefinition.getName());

        Runnable taskWithRetry = () -> {
            int retryCount = 0;
            boolean success = false;
            Exception lastException = null;

            while (retryCount < taskDefinition.getMaxRetries() && !success) {
                try {
                    task.run();
                    success = true;
                    logger.info("任务 {} 执行成功", taskDefinition.getName());
                } catch (Exception e) {
                    lastException = e;
                    retryCount++;
                    logger.error("任务 {} 执行失败 (尝试 {}/{}): {}",
                            taskDefinition.getName(), retryCount,
                            taskDefinition.getMaxRetries(), e.getMessage(), e);

                    if (retryCount < taskDefinition.getMaxRetries()) {
                        try {
                            Thread.sleep(taskDefinition.getRetryInterval());
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            logger.error("任务 {} 重试时被中断: {}", taskDefinition.getName(), ie.getMessage(), ie);
                            if (taskDefinition.isAlertOnFailure() && alertService != null) {
                                alertService.sendTaskFailureAlert(taskDefinition, ie, retryCount);
                            }
                            break;
                        }
                    } else if (taskDefinition.isAlertOnFailure() && alertService != null) {
                        logger.error("任务 {} 执行失败，已达到最大重试次数，发送报警", taskDefinition.getName());
                        alertService.sendTaskFailureAlert(taskDefinition, lastException, retryCount);
                    }
                }
            }
        };

        // 根据任务类型选择不同的线程池
        try {
            if (taskDefinition.getSchedulerTaskType() == TaskDefinition.SchedulerTaskType.IO_INTENSIVE) {
                ThreadPoolUtils.executeIoTask(taskWithRetry);
            } else {
                ThreadPoolUtils.executeCpuTask(taskWithRetry);
            }
        } catch (Exception e) {
            logger.error("提交任务到线程池失败: {}", e.getMessage(), e);
            if (taskDefinition.isAlertOnFailure() && alertService != null) {
                alertService.sendSystemAlert("提交任务" + taskDefinition.getName() + "到线程池失败: " + e.getMessage());
            }
        }
    }

    @Override
    public String getType() {
        return EXECUTOR_TYPE;
    }
}
