package com.astenamic.new_discovery.schedulerV2;

import com.astenamic.new_discovery.schedulerV2.config.SchedulerProperties;
import com.astenamic.new_discovery.schedulerV2.executor.TaskExecutor;
import com.astenamic.new_discovery.schedulerV2.manager.TaskManager;
import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;
import com.astenamic.new_discovery.common.util.ThreadPoolUtils;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 定时任务系统引导启动类
 */
@Component
public class SchedulerV2Core {

    private static final Logger logger = LoggerFactory.getLogger(SchedulerV2Core.class);
    private boolean initialized = false;

    private final TaskManager taskManager;
    private final SchedulerProperties properties;


    public SchedulerV2Core(TaskManager taskManager, SchedulerProperties properties) {
        this.taskManager = taskManager;
        this.properties = properties;
    }

    @PostConstruct
    public void init() {
        this.initialized = false;
        logger.info("初始化定时任务系统...");
        logger.info("初始化线程池...");
        ThreadPoolUtils.getCpuThreadPool();
        ThreadPoolUtils.getIoThreadPool();
        String poolStatus = ThreadPoolUtils.getPoolStatus();
        logger.info("线程池初始化完成: {}", poolStatus);
        initialized = true;
        logger.info("定时任务系统初始化完成");
    }


    @Scheduled(cron = "0 * * * * ?") // 每分钟执行一次
    public void run() {
        if (!initialized || !properties.isEnabled()) {
            return;
        }
        Map<TaskDefinition, Runnable> tasks = getTasks();
        if (tasks.isEmpty()) {
            logger.debug("当前时间点没有需要执行的任务");
            return;
        }
        executeTasks(tasks);
    }

    /**
     * 获取当前时间需要执行的任务
     */
    private Map<TaskDefinition, Runnable> getTasks() {
        return taskManager.getTasksDueAtTime(LocalDateTime.now());
    }

    /**
     * 执行符合条件的任务
     */
    private void executeTasks(Map<TaskDefinition, Runnable> tasksToExecute) {
        logger.info("发现 {} 个需要执行的任务", tasksToExecute.size());
        for (Map.Entry<TaskDefinition, Runnable> entry : tasksToExecute.entrySet()) {
            TaskDefinition taskDefinition = entry.getKey();
            Runnable task = entry.getValue();
            TaskExecutor executor = taskManager.getExecutor(taskDefinition);
            logger.info("提交任务[{}]到执行器[{}]执行", taskDefinition.getName(), executor.getType());
            executor.execute(taskDefinition, task);
        }
    }
}