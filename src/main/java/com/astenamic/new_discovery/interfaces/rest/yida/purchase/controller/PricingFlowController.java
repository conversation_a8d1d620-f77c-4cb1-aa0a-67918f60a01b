package com.astenamic.new_discovery.interfaces.rest.yida.purchase.controller;

import com.astenamic.new_discovery.biz.purchase.application.PricingCommandDispatcher;
import com.astenamic.new_discovery.biz.purchase.application.command.StartProductPricingFlowCmd;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.*;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.rest.yida.YidaConnector;
import com.astenamic.new_discovery.interfaces.rest.yida.purchase.dto.GeneratePricingOrderRequest;
import com.astenamic.new_discovery.interfaces.rest.yida.purchase.dto.ImportPricingReviewRequest;
import com.astenamic.new_discovery.interfaces.rest.yida.purchase.dto.PricingReviewRequest;
import com.astenamic.new_discovery.common.util.EasyExcelUtil;
import com.astenamic.new_discovery.common.util.ThreadPoolUtils;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 货品定价流程
 */
@Slf4j
@YidaConnector("/pricing")
@AllArgsConstructor
public class PricingFlowController {

    private final PricingCommandDispatcher commandDispatcher;

    private final StartProductPricingFlowCmd startProductPricingFlowCmd;

    /**
     * 发起供应商报价流程接口
     */
    @PostMapping("/startSupplierPricingFlow")
    public Result<Void> startSupplierPricingFlow(PricingReviewRequest request) {
        if (StringUtils.isEmpty(request.getPricingOrderNumber())) {
            return Result.fail("定价单号不能为空!");
        }
        ThreadPoolUtils.getIoThreadPool().execute(() -> {
            commandDispatcher.dispatch(new StartSupplierQuotationReq(request.getPricingOrderNumber()));
        });
        return Result.ok();
    }

    /**
     * 发起货品供应商报价流程接口
     */
    @PostMapping("/startQuotationFlow/{pricingOrderNumber}")
    public Result<Void> startQuotationFlow(@PathVariable("pricingOrderNumber") String pricingOrderNumber) {
        if (StringUtils.isBlank(pricingOrderNumber)) {
            return Result.fail("定价单号不能为空!");
        }
        ThreadPoolUtils.getIoThreadPool().execute(() -> {
            commandDispatcher.dispatch(new StartQuotationReq(pricingOrderNumber));
        });
        return Result.ok();
    }

    /**
     * 发起市调流程接口
     */


    /**
     * 生成定价评审表接口
     */
    @PostMapping("/generatePricingReviewSheet")
    public Result<Void> generatePricingReviewSheet(PricingReviewRequest request) {
        if (StringUtils.isEmpty(request.getPricingOrderNumber())) {
            return Result.fail("定价单号不能为空!");
        }
        ThreadPoolUtils.getIoThreadPool().execute(() -> {
            commandDispatcher.dispatch(new CreatePricingReviewReq(request.getPricingType(), request.getPricingOrderNumber(), request.getRequirementCode()));
        });
        return Result.ok();
    }

    /**
     * 导出定价评审表
     */
    @GetMapping("/exportPricingReviewSheet/{pricingOrderNumber}")
    public void exportPricingReviewSheet(HttpServletResponse response, @PathVariable("pricingOrderNumber") String pricingOrderNumber) {
        if (StringUtils.isEmpty(pricingOrderNumber)) {
            throw new BizException("定价单号不能为空!");
        }
        EasyExcelUtil.initResponse(response, "定价评审表-" + pricingOrderNumber + ".xlsx");
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            commandDispatcher.dispatch(new ExportPricingReviewReq(outputStream, pricingOrderNumber));
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 导出定价评审表
     */
    @GetMapping("/exportPricingReviewSheetV2/{pricingOrderNumber}")
    public void exportPricingReviewSheetV2(HttpServletResponse response, @PathVariable("pricingOrderNumber") String pricingOrderNumber) {
        if (StringUtils.isEmpty(pricingOrderNumber)) {
            throw new BizException("定价单号不能为空!");
        }
        EasyExcelUtil.initResponse(response, "定价评审表-" + pricingOrderNumber + ".xlsx");
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            commandDispatcher.dispatch(new ExportPricingReviewV2Req(outputStream, pricingOrderNumber));
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 导入定价评审表
     */
    @PostMapping("/importPricingReviewSheet")
    public Result<Void> importPricingReviewSheet(@RequestBody ImportPricingReviewRequest params) {
        commandDispatcher.dispatch(new ImportPricingReviewReq(params.getDownloadUrl(), params.getPricingOrderNumber()));
        return Result.ok();
    }

    /**
     * 导入定价评审表
     */
    @PostMapping("/importPricingReviewSheetV2")
    public Result<Void> importPricingReviewSheetV2(@RequestBody ImportPricingReviewRequest params) {
        commandDispatcher.dispatch(new ImportPricingReviewV2Req(params.getDownloadUrl(), params.getPricingOrderNumber()));
        return Result.ok();
    }


    /**
     * 生成定价单接口
     */
    @PostMapping("/generatePricingOrder")
    public Result<Void> generatePricingOrder(GeneratePricingOrderRequest request) {
        if (StringUtils.isEmpty(request.getPricingOrderNumber()) ||
                StringUtils.isEmpty(request.getPricingType())) {
            throw new BizException(Result.ErrorCode.VALIDATION_FAILED);
        }
        ThreadPoolUtils.getIoThreadPool().execute(() -> {
            commandDispatcher.dispatch(new CreatePricingOrderReq(request.getPricingType(), request.getPricingOrderNumber(), request.getRequirementCode()));
        });
        return Result.ok();
    }

    /**
     * 同步定价单接口
     */
    @GetMapping("/syncPricingOrder/{code}")
    public Result<Void> syncPricingOrder(@PathVariable("code") String code) {
        if (StringUtils.isEmpty(code)) {
            return Result.fail("定价单号不能为空!");
        }
        commandDispatcher.dispatch(new SyncPricingOrderReq(code));
        return Result.ok();
    }

    /**
     * 同步货品类别到定价单
     */
    @GetMapping("/syncProductCategory/{code}")
    public Result<Void> syncProductCategory(@PathVariable("code") String code) {
        if (StringUtils.isEmpty(code)) {
            return Result.fail("定价单号不能为空!");
        }
        ThreadPoolUtils.getIoThreadPool().execute(() -> {
            commandDispatcher.dispatch(new SyncPriceSheetCategoryReq(code));
        });
        return Result.ok();
    }


    /**
     * 手动发起定价评审
     */
    @GetMapping("/manualStartFlow")
    public Result<Void> manualStartFlow(@RequestParam("cat") String cat) {
        if (StringUtils.isBlank(cat)) {
            return Result.fail("货品分类不能为空");
        }
        ThreadPoolUtils.getIoThreadPool().execute(() -> {
            startProductPricingFlowCmd.startFlow(cat);
        });
        return Result.ok();
    }
}
