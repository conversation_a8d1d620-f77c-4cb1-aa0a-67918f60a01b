package com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolRecord;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PatrolRecordDao extends JpaRepository<PatrolRecord, Long> {

    /**
     * 根据业务创建时间范围查找巡店记录（包含详情）
     */
    @EntityGraph(attributePaths = {"patrolDetailList"})
    List<PatrolRecord> findByBizCreateTimeBetween(LocalDateTime start, LocalDateTime end);

    /**
     * 根据业务记录ID查找巡店记录（包含详情）
     */
    @EntityGraph(attributePaths = {"patrolDetailList"})
    Optional<PatrolRecord> findByRecordId(Long recordId);

    /**
     * 根据业务创建时间范围和删除标识查找巡店记录（包含详情）
     */
    @EntityGraph(attributePaths = {"patrolDetailList"})
    List<PatrolRecord> findByBizCreateTimeBetweenAndDelFlag(LocalDateTime start, LocalDateTime end, String delFlag);

    /**
     * 根据业务记录ID和删除标识查找巡店记录（包含详情）
     */
    @EntityGraph(attributePaths = {"patrolDetailList"})
    Optional<PatrolRecord> findByRecordIdAndDelFlag(Long recordId, String delFlag);

}

