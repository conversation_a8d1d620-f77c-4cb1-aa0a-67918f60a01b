package com.astenamic.new_discovery.biz.business.domain.repository;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.TrainingClassification;

import java.util.List;
import java.util.Optional;

/**
 * 培训分类仓储接口
 */
public interface TrainingClassificationRepository {

    /**
     * 查找所有培训分类
     *
     * @return 所有培训分类列表
     */
    List<TrainingClassification> findAll();

    /**
     * 根据业务ID查找培训分类
     *
     * @param bizId 业务ID
     * @return 培训分类
     */
    Optional<TrainingClassification> findByBizId(Long bizId);

    /**
     * 根据父级ID查找子分类
     *
     * @param parentId 父级ID
     * @return 子分类列表
     */
    List<TrainingClassification> findByParentId(Long parentId);

    /**
     * 检查业务ID是否存在
     *
     * @param bizId 业务ID
     * @return 是否存在
     */
    boolean existsByBizId(Long bizId);

    /**
     * 保存单个培训分类
     *
     * @param entity 要保存的实体
     * @return 保存后的实体ID
     */
    String save(TrainingClassification entity);

    /**
     * 批量保存培训分类
     *
     * @param entities 要保存的实体列表
     * @return 保存后的实体ID列表
     */
    List<String> saveAll(List<TrainingClassification> entities);

    /**
     * 根据ID删除培训分类
     *
     * @param id 实体ID
     * @return 删除后的实体ID
     */
    String deleteById(Long id);

    /**
     * 根据业务ID删除培训分类
     *
     * @param bizId 业务ID
     * @return 删除后的实体ID
     */
    String deleteByBizId(Long bizId);

    /**
     * 批量删除培训分类
     *
     * @param entities 要删除的实体列表
     */
    void deleteAll(List<TrainingClassification> entities);
}
