package com.astenamic.new_discovery.biz.business.application.service;

import com.astenamic.new_discovery.biz.business.application.decorator.ShopRelationDecorator;
import com.astenamic.new_discovery.biz.business.application.mapper.MaterialReportMapper;
import com.astenamic.new_discovery.biz.business.domain.entity.CostLossRateFlow;
import com.astenamic.new_discovery.biz.business.domain.repository.CostLossRateFlowRepository;
import com.astenamic.new_discovery.biz.business.domain.valueobject.LossRateItem;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao.DayWeeklogiSmLossReportJpaDao;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dto.MaterialReportDTO;
import com.astenamic.new_discovery.common.util.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CostLossRateFlowAppService {

    private final MaterialReportMapper materialReportMapper;

    private final DayWeeklogiSmLossReportJpaDao dayWeeklogiSmLossReportJpaDao;

    private final ShopRelationDecorator shopRelationDecorator;

    private final CostLossRateFlowRepository costLossRateFlowYidaAdapter;

    /**
     * 发起周度原材料预警
     */
    public void weeklyMaterialReport(LocalDateTime targetTime) {
        log.info("开始发起周度原材料预警,{}", targetTime);
        LocalDate[] weekRange = TimeUtils.getWeekDateRange(targetTime, 1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startDate = formatter.format(weekRange[0]);
        String endDate = formatter.format(weekRange[1]);
        List<MaterialReportDTO> dtos = dayWeeklogiSmLossReportJpaDao.weeklyTopLoss(startDate, endDate);
        if (!dtos.isEmpty()) {
            List<LossRateItem> lossRateItems = materialReportMapper.toLossRateItems(dtos);

            List<CostLossRateFlow> newDatas = buildFlowByItems(lossRateItems, targetTime, startDate + "~" + endDate);

            newDatas = shopRelationDecorator.costLossRateFlowDecorator(newDatas);

            batchLaunchProcess(targetTime, newDatas);
        }
    }


    /**
     * 聚合成本损耗率子表创建流程
     *
     * @param lossRateItems 损耗率项目列表
     * @param targetTime    目标时间
     * @param dateRange     日期范围
     * @return 按照商店ID分组的成本损耗率流程列表
     */
    public List<CostLossRateFlow> buildFlowByItems(List<LossRateItem> lossRateItems,
                                                   LocalDateTime targetTime,
                                                   String dateRange) {
        // 参数校验
        if (lossRateItems == null || lossRateItems.isEmpty()) {
            log.warn("创建成本损耗率流程失败：损耗率项目列表为空");
            return List.of();
        }

        if (targetTime == null) {
            log.warn("创建成本损耗率流程失败：目标时间为空");
            return List.of();
        }

        if (dateRange == null || dateRange.isBlank()) {
            log.warn("创建成本损耗率流程失败：日期范围为空");
            return List.of();
        }

        return lossRateItems.stream()
                .filter(item -> item.getShopId() != null)
                .collect(Collectors.groupingBy(LossRateItem::getShopId))
                .entrySet().stream()
                .map(e -> {
                    CostLossRateFlow costLossRateFlow = new CostLossRateFlow();
                    costLossRateFlow.setShopId(e.getKey());
                    costLossRateFlow.setDate(targetTime);
                    costLossRateFlow.setDateRange(dateRange);
                    costLossRateFlow.setLossRateList(e.getValue());
                    return costLossRateFlow;
                }).toList();
    }

    /**
     * 批量发起流程
     */
    public void batchLaunchProcess(LocalDateTime targetTime, List<CostLossRateFlow> flows) {
        if (flows == null || flows.isEmpty()) {
            log.warn("货品定价流程列表为空，时间: {}", targetTime);
            return;
        }
        costLossRateFlowYidaAdapter.findByWeek(targetTime).forEach(
                oldDatum -> {
                    for (CostLossRateFlow newDatum : flows) {
                        if (oldDatum.getShopId().equals(newDatum.getShopId())) {
                            newDatum.setInstanceId(oldDatum.getInstanceId());
                            break;
                        }
                    }
                }
        );
        costLossRateFlowYidaAdapter.saveAll(flows);
    }

}
