package com.astenamic.new_discovery.biz.business.infrastructure.jpa;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.TrainingClassification;
import com.astenamic.new_discovery.biz.business.domain.repository.TrainingClassificationRepository;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao.TrainingClassificationJpaDao;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao.TrainingClassificationJpaDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 培训分类 JPA 适配器
 */
@Repository
@RequiredArgsConstructor
public class TrainingClassificationJpaAdapter implements TrainingClassificationRepository {

    private final TrainingClassificationJpaDao jpaDao;

    private static final String NORMAL_FLAG = "0";

    private static final String DELETED_FLAG = "1";

    @Override
    public List<TrainingClassification> findAll() {
        return jpaDao.findByDelFlagOrderBySort(NORMAL_FLAG);
    }

    @Override
    public Optional<TrainingClassification> findByBizId(Long bizId) {
        return jpaDao.findByBizIdAndDelFlag(bizId, NORMAL_FLAG);
    }

    @Override
    public List<TrainingClassification> findByParentId(Long parentId) {
        return jpaDao.findByParentIdAndDelFlagOrderBySort(parentId, NORMAL_FLAG);
    }

    @Override
    public boolean existsByBizId(Long bizId) {
        return jpaDao.existsByBizIdAndDelFlag(bizId, NORMAL_FLAG);
    }

    @Override
    @Transactional
    public String save(TrainingClassification entity) {
        try {
            TrainingClassification saved = jpaDao.save(entity);
            return saved.getId().toString();
        } catch (Exception e) {
            throw new RuntimeException("保存培训分类失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public List<String> saveAll(List<TrainingClassification> entities) {
        try {
            List<TrainingClassification> saved = jpaDao.saveAll(entities);
            return saved.stream()
                    .map(entity -> entity.getId().toString())
                    .toList();
        } catch (Exception e) {
            throw new RuntimeException("批量保存培训分类失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public String deleteById(Long id) {
        try {
            Optional<TrainingClassification> entityOpt = jpaDao.findById(id);
            if (entityOpt.isEmpty()) {
                throw new RuntimeException("培训分类不存在，ID：" + id);
            }

            TrainingClassification entity = entityOpt.get();
            entity.setDelFlag(DELETED_FLAG);
            TrainingClassification saved = jpaDao.save(entity);
            return saved.getId().toString();
        } catch (Exception e) {
            throw new RuntimeException("删除培训分类失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public String deleteByBizId(Long bizId) {
        try {
            Optional<TrainingClassification> entityOpt = jpaDao.findByBizId(bizId);
            if (entityOpt.isEmpty()) {
                throw new RuntimeException("培训分类不存在，业务ID：" + bizId);
            }

            TrainingClassification entity = entityOpt.get();
            entity.setDelFlag(DELETED_FLAG);
            TrainingClassification saved = jpaDao.save(entity);
            return saved.getId().toString();
        } catch (Exception e) {
            throw new RuntimeException("根据业务ID删除培训分类失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void deleteAll(List<TrainingClassification> entities) {
        try {
            entities.forEach(entity -> entity.setDelFlag(DELETED_FLAG));
            jpaDao.saveAll(entities);
        } catch (Exception e) {
            throw new RuntimeException("批量删除培训分类失败：" + e.getMessage(), e);
        }
    }
}
