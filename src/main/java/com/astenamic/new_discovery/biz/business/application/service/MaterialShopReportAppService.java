package com.astenamic.new_discovery.biz.business.application.service;

import com.astenamic.new_discovery.biz.business.application.mapper.MaterialReportMapper;
import com.astenamic.new_discovery.biz.business.domain.entity.MaterialShopReport;
import com.astenamic.new_discovery.biz.business.application.decorator.ShopRelationDecorator;
import com.astenamic.new_discovery.biz.business.domain.repository.MaterialShopReportRepository;
import com.astenamic.new_discovery.biz.business.domain.valueobject.MaterialReport;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao.DaySmLossReportJpaDao;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dto.MaterialReportDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialShopReportAppService {


    private final ShopRelationDecorator shopRelationDecorator;


    private final DaySmLossReportJpaDao daySmLossReportJpaDao;


    private final MaterialShopReportRepository materialShopReportYidaAdapter;

    private final MaterialReportMapper materialReportMapper;

    public void syncMaterialDaily(LocalDateTime day) {
        List<MaterialShopReport> newData = getListByTimeFromDB(day);
        getListByTimeFromYida(day).forEach(
                old -> {
                    newData.forEach(newDatw -> {
                        if (old.getShopId().equals(newDatw.getShopId())) {
                            newDatw.setObjectId(old.getObjectId());
                        }
                    });
                }
        );
        this.materialShopReportYidaAdapter.saveAll(newData);
    }

    public List<MaterialShopReport> getListByTimeFromDB(LocalDateTime targetTime) {
        List<MaterialShopReport> result = Optional.ofNullable(targetTime)
                .map(this::getMaterialReportListByTime)
                .filter(list -> !list.isEmpty())
                .map(list -> list.stream()
                        .collect(Collectors.groupingBy(MaterialReport::getShopId))
                        .entrySet().stream()
                        .map(e -> {
                            MaterialShopReport report = new MaterialShopReport(e.getKey(), targetTime);
                            report.setMaterialReports(e.getValue());
                            return report;
                        })
                        .collect(Collectors.toList())
                )
                .orElseGet(Collections::emptyList);

        return shopRelationDecorator
                .materialShopReportDecorator(result).stream()
                .filter(r -> r.getShopSysId() != null && !r.getShopSysId().isEmpty())
                .toList();
    }

    public List<MaterialShopReport> getListByTimeFromYida(LocalDateTime targetTime) {
        return materialShopReportYidaAdapter.findByTime(targetTime);
    }

    public List<MaterialReport> getMaterialReportListByTime(LocalDateTime targetTime) {
        if (targetTime == null) {
            return List.of();
        }
        List<MaterialReportDTO> materialReportDTOs = daySmLossReportJpaDao.findMaterialReportByTime(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(targetTime));
        if (materialReportDTOs == null || materialReportDTOs.isEmpty()) {
            return List.of();
        }
        return materialReportMapper.toMaterialReports(materialReportDTOs);
    }

}
