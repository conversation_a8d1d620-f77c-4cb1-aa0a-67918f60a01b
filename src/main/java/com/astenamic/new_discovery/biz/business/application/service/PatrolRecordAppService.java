package com.astenamic.new_discovery.biz.business.application.service;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolDetail;
import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolRecord;
import com.astenamic.new_discovery.biz.business.domain.repository.PatrolRecordRepository;
import com.astenamic.new_discovery.biz.business.application.mapper.PatrolDetailMapper;
import com.astenamic.new_discovery.biz.business.application.mapper.PatrolRecordMapper;
import com.astenamic.new_discovery.external.client.cool.vision.service.PatrolDetailListService;
import com.astenamic.new_discovery.external.client.cool.vision.service.PatrolListService;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolDetailDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolRecordDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 巡店记录同步应用服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PatrolRecordAppService {

    /* -------- 依赖注入 -------- */
    private final PatrolListService patrolListService;
    private final PatrolDetailListService patrolDetailListService;

    private final PatrolRecordMapper patrolRecordMapper;
    private final PatrolDetailMapper patrolDetailMapper;

    private final PatrolRecordRepository patrolRecordRepository;

    /**
     * 同步指定日期（targetTime 当天）的巡店记录及详情到本地库
     * 实现完整的新增、更新、删除逻辑
     *
     * @param targetTime 需要同步的目标日期（任意一天的时间，即取该天 00:00-23:59）
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncPatrolRecord(LocalDateTime targetTime) {

        if (targetTime == null) {
            log.warn("【巡店同步】targetTime 为空，直接返回");
            return;
        }

        log.info("【巡店同步】开始同步日期：{}", targetTime.toLocalDate());

        try {
            /* -------- 1. 拉取远程数据 -------- */
            List<PatrolRecordDTO> recordDTOs = patrolListService.getPatrolList(targetTime);

            // 获取远程数据的业务ID集合
            Set<Long> remoteRecordIds = recordDTOs.stream()
                    .map(PatrolRecordDTO::getRecordId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 获取本地当天的所有数据
            List<PatrolRecord> localRecords = patrolRecordRepository.findByTime(targetTime);

            /* -------- 2. 处理完整同步逻辑（新增、更新、删除） -------- */
            List<PatrolRecord> entitiesToSave = new ArrayList<>();
            int updatedCount = 0;
            int newCount = 0;
            int deletedCount = 0;

            // 处理新增和更新
            for (PatrolRecordDTO recDto : recordDTOs) {
                if (recDto.getRecordId() != null) {
                    var existingOpt = patrolRecordRepository.findByRecordId(recDto.getRecordId());

                    if (existingOpt.isPresent()) {
                        // 更新已存在的记录
                        PatrolRecord existing = existingOpt.get();
                        updateExistingRecord(existing, recDto, targetTime);
                        entitiesToSave.add(existing);
                        updatedCount++;
                    } else {
                        // 新增记录
                        PatrolRecord entity = createNewRecord(recDto, targetTime);
                        entitiesToSave.add(entity);
                        newCount++;
                    }
                }
            }

            // 处理删除
            for (PatrolRecord localRecord : localRecords) {
                if (localRecord.getRecordId() != null && !remoteRecordIds.contains(localRecord.getRecordId())) {
                    localRecord.setDelFlag("1");
                    entitiesToSave.add(localRecord);
                    deletedCount++;
                }
            }

            /* -------- 3. 批量保存到数据库 -------- */
            if (!entitiesToSave.isEmpty()) {
                List<String> savedIds = patrolRecordRepository.saveAll(entitiesToSave);
                log.info("【巡店同步】同步完成，新增{}条，更新{}条，删除{}条，总计处理{}条数据",
                        newCount, updatedCount, deletedCount, savedIds.size());
            } else {
                log.info("【巡店同步】没有数据变更");
            }

        } catch (Exception e) {
            log.error("【巡店同步】同步失败", e);
            throw new RuntimeException("巡店记录同步失败：" + e.getMessage(), e);
        }
    }

    /**
     * 创建新的巡店记录聚合
     *
     * @param recDto     巡店记录DTO
     * @param targetTime 目标时间
     * @return 新的巡店记录聚合
     */
    private PatrolRecord createNewRecord(PatrolRecordDTO recDto, LocalDateTime targetTime) {
        PatrolRecord entity = patrolRecordMapper.toEntity(recDto);

        // 拉取详情数据并通过聚合根方法添加
        List<PatrolDetailDTO> detailDTOs = patrolDetailListService.getPatrolDetailList(recDto.getRecordId(), targetTime);
        List<PatrolDetail> detailEntities = patrolDetailMapper.toEntityList(detailDTOs);

        // 使用聚合根方法设置详情
        entity.setPatrolDetailList(detailEntities);

        return entity;
    }

    /**
     * 更新已存在的巡店记录聚合
     *
     * @param existing   已存在的聚合根
     * @param recDto     新的DTO数据
     * @param targetTime 目标时间
     */
    private void updateExistingRecord(PatrolRecord existing, PatrolRecordDTO recDto, LocalDateTime targetTime) {
        // 更新主记录字段（通过mapper重新映射）
        PatrolRecord updated = patrolRecordMapper.toEntity(recDto);
        copyUpdatedFields(existing, updated);

        // 重新拉取详情数据并通过聚合根方法更新
        List<PatrolDetailDTO> detailDTOs = patrolDetailListService.getPatrolDetailList(recDto.getRecordId(), targetTime);
        List<PatrolDetail> detailEntities = patrolDetailMapper.toEntityList(detailDTOs);

        // 使用聚合根方法更新详情（会自动处理orphanRemoval）
        existing.setPatrolDetailList(detailEntities);
    }

    /**
     * 复制更新的字段到已存在的实体
     *
     * @param existing 已存在的实体
     * @param updated  更新后的实体
     */
    private void copyUpdatedFields(PatrolRecord existing, PatrolRecord updated) {
        BeanUtils.copyProperties(updated, existing,
                "id",
                "delFlag",
                "createTime",
                "updateTime",
                "patrolDetailList"
        );
    }
}
