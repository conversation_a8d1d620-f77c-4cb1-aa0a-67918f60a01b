package com.astenamic.new_discovery.biz.business.domain.entity.cool;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 学习项目培训分类实体
 */
@Data
@Entity(name = "xfx_cool_training_classification")
public class TrainingClassification {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 删除标识：0-正常 1-删除
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag = "0";

    /**
     * 创建时间
     */
    @Column(name = "create_time", updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @PrePersist
    protected void preInsert() {
        this.createTime = this.updateTime = LocalDateTime.now();
    }

    @PreUpdate
    protected void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 分类路径（不含自身，逗号分隔）
     */
    @Column(name = "parent_id_chain", columnDefinition = "text")
    private String parentIdChain;

    /**
     * 级别：1→一级 2→二级 3→三级
     */
    @Column(name = "level")
    private Integer level;

    /**
     * 描述
     */
    @Column(name = "description", columnDefinition = "text")
    private String description;

    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;

    /**
     * 类型名称
     */
    @Column(name = "title", length = 128)
    private String title;

    /**
     * 类型：0 系统预制 1 业务
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 是否启用
     */
    @Column(name = "enabled")
    private Boolean enabled;

    /**
     * 父节点是否启用
     */
    @Column(name = "parent_enable")
    private Boolean parentEnable;

    /**
     * 更新人 ID
     */
    @Column(name = "update_user")
    private Long updateUser;

    /**
     * 父级 ID（无父节点为 0）
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 分类名
     */
    @Column(name = "name", length = 128)
    private String name;

    /**
     * 是否禁用
     */
    @Column(name = "disabled")
    private Boolean disabled;

    /**
     * 创建人 ID
     */
    @Column(name = "create_user")
    private Long createUser;

    /**
     * 分类 key（与 id 一致）
     */
    @Column(name = "key_value")
    private Long keyValue;

    /**
     * 业务ID（来自外部系统的原始ID）
     */
    @Column(name = "biz_id")
    private Long bizId;

    /**
     * 业务创建时间
     */
    @Column(name = "biz_create_time")
    private LocalDateTime bizCreateTime;

    /**
     * 业务更新时间
     */
    @Column(name = "biz_update_time")
    private LocalDateTime bizUpdateTime;
}
