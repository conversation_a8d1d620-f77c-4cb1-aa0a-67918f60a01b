package com.astenamic.new_discovery.biz.business.domain.repository;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 巡店记录聚合根仓储接口
 * 负责管理PatrolRecord聚合（包含PatrolDetail）
 */
public interface PatrolRecordRepository {

    /**
     * 根据时间查找巡店记录
     *
     * @param targetTime 目标时间
     * @return 巡店记录列表
     */
    List<PatrolRecord> findByTime(LocalDateTime targetTime);

    /**
     * 根据业务记录ID查找巡店记录
     *
     * @param recordId 业务记录ID
     * @return 巡店记录
     */
    Optional<PatrolRecord> findByRecordId(Long recordId);

    /**
     * 根据时间范围查找所有巡店记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 巡店记录列表
     */
    List<PatrolRecord> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 检查业务记录ID是否存在
     *
     * @param recordId 业务记录ID
     * @return 是否存在
     */
    boolean existsByRecordId(Long recordId);

    /**
     * 保存单个巡店记录
     *
     * @param entity 要保存的实体
     * @return 保存后的实体ID
     */
    String save(PatrolRecord entity);

    /**
     * 批量保存巡店记录
     *
     * @param entities 要保存的实体列表
     * @return 保存后的实体ID列表
     */
    List<String> saveAll(List<PatrolRecord> entities);

    /**
     * 根据业务记录ID删除巡店记录（软删除）
     *
     * @param recordId 业务记录ID
     * @return 删除后的实体ID
     */
    String deleteByRecordId(Long recordId);

    /**
     * 批量删除巡店记录（软删除）
     *
     * @param entities 要删除的实体列表
     */
    void deleteAll(List<PatrolRecord> entities);
}
