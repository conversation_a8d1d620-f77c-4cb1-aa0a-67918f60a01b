package com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.TrainingClassification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 培训分类 JPA 数据访问对象
 */
@Repository
public interface TrainingClassificationJpaDao extends JpaRepository<TrainingClassification, Long> {

    /**
     * 根据业务ID查找培训分类
     *
     * @param bizId 业务ID
     * @return 培训分类
     */
    Optional<TrainingClassification> findByBizIdAndDelFlag(Long bizId, String delFlag);

    /**
     * 根据父级ID查找子分类
     *
     * @param parentId 父级ID
     * @param delFlag 删除标识
     * @return 子分类列表
     */
    List<TrainingClassification> findByParentIdAndDelFlagOrderBySort(Long parentId, String delFlag);

    /**
     * 查找所有未删除的分类
     *
     * @param delFlag 删除标识
     * @return 分类列表
     */
    List<TrainingClassification> findByDelFlagOrderBySort(String delFlag);

    /**
     * 检查业务ID是否存在
     *
     * @param bizId 业务ID
     * @param delFlag 删除标识
     * @return 是否存在
     */
    boolean existsByBizIdAndDelFlag(Long bizId, String delFlag);

    /**
     * 根据业务ID查找培训分类（包含已删除）
     *
     * @param bizId 业务ID
     * @return 培训分类
     */
    Optional<TrainingClassification> findByBizId(Long bizId);
}
