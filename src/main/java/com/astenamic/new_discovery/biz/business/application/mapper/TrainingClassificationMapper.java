package com.astenamic.new_discovery.biz.business.application.mapper;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.TrainingClassification;
import com.astenamic.new_discovery.common.util.MapperUtil;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.TrainingClassificationDTO;
import org.mapstruct.*;

import java.util.List;

/**
 * 学习项目培训分类 Mapper（DTO ⇄ Entity）
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = MapperUtil.class)
public interface TrainingClassificationMapper {

    @Mappings({
            @Mapping(source = "id", target = "bizId"),
            @Mapping(source = "key", target = "keyValue"),
            @Mapping(source = "createTime", target = "bizCreateTime", qualifiedByName = "longToLocalDateTime"),
            @Mapping(source = "updateTime", target = "bizUpdateTime", qualifiedByName = "longToLocalDateTime"),
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "delFlag", constant = "0"),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true)
    })
    TrainingClassification toEntity(TrainingClassificationDTO dto);

    @Mappings({
            @Mapping(source = "bizId", target = "id"),
            @Mapping(source = "keyValue", target = "key"),
            @Mapping(source = "bizCreateTime", target = "createTime", qualifiedByName = "localDateTimeToLong"),
            @Mapping(source = "bizUpdateTime", target = "updateTime", qualifiedByName = "localDateTimeToLong")
    })
    TrainingClassificationDTO toDTO(TrainingClassification entity);

    List<TrainingClassification> toEntityList(List<TrainingClassificationDTO> dtoList);

    List<TrainingClassificationDTO> toDTOList(List<TrainingClassification> entityList);


}
