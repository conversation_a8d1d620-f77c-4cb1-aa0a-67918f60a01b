package com.astenamic.new_discovery.biz.business.application.mapper;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolDetail;
import com.astenamic.new_discovery.common.util.MapperUtil;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolDetailDTO;
import org.mapstruct.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = MapperUtil.class)
public interface PatrolDetailMapper {

    @Mappings({
            @Mapping(source = "createTime", target = "bizCreateTime", qualifiedByName = "longToLocalDateTime"),
            @Mapping(source = "subBeginTime", target = "subBeginTime", qualifiedByName = "longToLocalDateTime"),
            @Mapping(source = "subEndTime", target = "subEndTime", qualifiedByName = "longToLocalDateTime"),
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "delFlag", constant = "0"),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true)
    })
    PatrolDetail toEntity(PatrolDetailDTO dto);


    @Mappings({
            @Mapping(source = "bizCreateTime", target = "createTime", qualifiedByName = "localDateTimeToLong"),
            @Mapping(source = "subBeginTime", target = "subBeginTime", qualifiedByName = "localDateTimeToLong"),
            @Mapping(source = "subEndTime", target = "subEndTime", qualifiedByName = "localDateTimeToLong")
    })
    PatrolDetailDTO toDTO(PatrolDetail entity);

    List<PatrolDetail> toEntityList(List<PatrolDetailDTO> dtoList);

    List<PatrolDetailDTO> toDTOList(List<PatrolDetail> entityList);

}
