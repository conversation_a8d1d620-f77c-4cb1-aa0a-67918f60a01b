package com.astenamic.new_discovery.biz.business.application.service;

import com.astenamic.new_discovery.biz.business.application.mapper.TrainingClassificationMapper;
import com.astenamic.new_discovery.biz.business.domain.entity.cool.TrainingClassification;
import com.astenamic.new_discovery.biz.business.domain.repository.TrainingClassificationRepository;
import com.astenamic.new_discovery.common.util.MapperUtil;
import com.astenamic.new_discovery.external.client.cool.college.service.TrainingClassificationApiService;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.TrainingClassificationDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.college.study.TrainingClassificationQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 培训分类同步应用服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingClassificationAppService {


    private final TrainingClassificationApiService trainingClassificationApiService;

    private final TrainingClassificationMapper trainingClassificationMapper;

    private final TrainingClassificationRepository trainingClassificationRepository;

    /**
     * 全量同步培训分类数据
     * 获取完整的父子结构分类树并扁平化存储
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncTrainingClassifications() {
        log.info("【培训分类同步】开始全量同步培训分类数据");

        try {
            /* -------- 1. 从远程API获取分类树 -------- */
            TrainingClassificationQuery query = TrainingClassificationQuery.builder()
                    .showAllFields(true)
                    .build();

            List<TrainingClassificationDTO> classificationTree = trainingClassificationApiService.list(query);

            if (classificationTree == null || classificationTree.isEmpty()) {
                log.info("【培训分类同步】远程API返回空数据");
                return;
            }

            /* -------- 2. 扁平化树形结构 -------- */
            List<TrainingClassificationDTO> flattenedList = flattenClassificationTree(classificationTree);
            log.info("【培训分类同步】扁平化后共{}条分类数据", flattenedList.size());

            /* -------- 3. 处理完整同步逻辑（新增、更新、删除） -------- */
            // 获取远程数据的业务ID集合
            Set<Long> remoteBizIds = flattenedList.stream()
                    .map(TrainingClassificationDTO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 获取本地所有数据
            List<TrainingClassification> localEntities = trainingClassificationRepository.findAll();

            List<TrainingClassification> entitiesToSave = new ArrayList<>();
            int updatedCount = 0;
            int newCount = 0;
            int deletedCount = 0;

            for (TrainingClassificationDTO dto : flattenedList) {
                if (dto.getId() != null) {
                    var existingOpt = trainingClassificationRepository.findByBizId(dto.getId());

                    if (existingOpt.isPresent()) {
                        // 更新已存在的记录
                        TrainingClassification existing = existingOpt.get();
                        updateExistingEntity(existing, dto);
                        entitiesToSave.add(existing);
                        updatedCount++;
                    } else {
                        // 新增记录
                        TrainingClassification entity = trainingClassificationMapper.toEntity(dto);
                        entitiesToSave.add(entity);
                        newCount++;
                    }
                }
            }

            for (TrainingClassification localEntity : localEntities) {
                if (localEntity.getBizId() != null && !remoteBizIds.contains(localEntity.getBizId())) {
                    localEntity.setDelFlag("1");
                    entitiesToSave.add(localEntity);
                    deletedCount++;
                }
            }

            /* -------- 4. 批量保存到数据库 -------- */
            if (!entitiesToSave.isEmpty()) {
                List<String> savedIds = trainingClassificationRepository.saveAll(entitiesToSave);
                log.info("【培训分类同步】全量同步完成，新增{}条，更新{}条，删除{}条，总计处理{}条数据",
                        newCount, updatedCount, deletedCount, savedIds.size());
            } else {
                log.info("【培训分类同步】没有数据变更");
            }

        } catch (Exception e) {
            log.error("【培训分类同步】同步失败", e);
            throw new RuntimeException("培训分类同步失败：" + e.getMessage(), e);
        }
    }


    /**
     * 扁平化分类树结构
     * 将树形结构的分类数据转换为扁平列表
     *
     * @param classificationTree 分类树
     * @return 扁平化的分类列表
     */
    private List<TrainingClassificationDTO> flattenClassificationTree(List<TrainingClassificationDTO> classificationTree) {
        List<TrainingClassificationDTO> result = new ArrayList<>();

        for (TrainingClassificationDTO classification : classificationTree) {
            // 添加当前节点
            result.add(classification);

            // 递归处理子节点
            if (classification.getChildren() != null && !classification.getChildren().isEmpty()) {
                List<TrainingClassificationDTO> childrenFlattened = flattenClassificationTree(classification.getChildren());
                result.addAll(childrenFlattened);
            }
        }

        return result;
    }

    /**
     * 更新已存在的实体
     *
     * @param existing 已存在的实体
     * @param dto      新的DTO数据
     */
    private void updateExistingEntity(TrainingClassification existing, TrainingClassificationDTO dto) {
        // 更新业务字段
        existing.setParentIdChain(dto.getParentIdChain());
        existing.setLevel(dto.getLevel());
        existing.setDescription(dto.getDescription());
        existing.setSort(dto.getSort());
        existing.setTitle(dto.getTitle());
        existing.setType(dto.getType());
        existing.setEnabled(dto.getEnabled());
        existing.setParentEnable(dto.getParentEnable());
        existing.setUpdateUser(dto.getUpdateUser());
        existing.setParentId(dto.getParentId());
        existing.setName(dto.getName());
        existing.setDisabled(dto.getDisabled());
        existing.setCreateUser(dto.getCreateUser());
        existing.setKeyValue(dto.getKey());

        // 更新业务时间
        if (dto.getCreateTime() != null) {
            existing.setBizCreateTime(MapperUtil.longToLocalDateTime(dto.getCreateTime()));
        }
        if (dto.getUpdateTime() != null) {
            existing.setBizUpdateTime(MapperUtil.longToLocalDateTime(dto.getUpdateTime()));
        }
    }
}
