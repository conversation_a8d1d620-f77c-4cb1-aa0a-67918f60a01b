package com.astenamic.new_discovery.biz.business.infrastructure.jpa;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolRecord;
import com.astenamic.new_discovery.biz.business.domain.repository.PatrolRecordRepository;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao.PatrolRecordDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 巡店记录聚合根JPA适配器
 * 通过JPA的级联操作管理整个聚合
 */
@Repository
@RequiredArgsConstructor
public class PatrolRecordJpaAdapter implements PatrolRecordRepository {

    private final PatrolRecordDao patrolRecordDao;

    private static final String NORMAL_FLAG = "0";
    private static final String DELETED_FLAG = "1";

    /**
     * 按日期查询巡店记录聚合（只查询未删除的记录）
     * JPA会自动级联加载PatrolDetail
     */
    @Override
    public List<PatrolRecord> findByTime(LocalDateTime targetTime) {
        if (targetTime == null) {
            return List.of();
        }
        LocalDateTime start = targetTime.with(LocalTime.MIN);
        LocalDateTime end = targetTime.with(LocalTime.MAX);

        return patrolRecordDao.findByBizCreateTimeBetweenAndDelFlag(start, end, NORMAL_FLAG);
    }

    /**
     * 根据recordId查询巡店记录聚合（只查询未删除的记录）
     * JPA会自动级联加载PatrolDetail
     */
    @Override
    public Optional<PatrolRecord> findByRecordId(Long recordId) {
        return patrolRecordDao.findByRecordIdAndDelFlag(recordId, NORMAL_FLAG);
    }

    /**
     * 保存巡店记录聚合
     * 使用@JoinColumn配置的orphanRemoval实现先删后插，避免重复数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String save(PatrolRecord entity) {
        try {
            /* -------- 参数校验 -------- */
            if (entity.getRecordId() == null) {
                throw new IllegalArgumentException("recordId 不能为空");
            }

            /* -------- 幂等处理：如果记录已存在，使用现有ID -------- */
            patrolRecordDao.findByRecordId(entity.getRecordId())
                    .ifPresent(existing -> {
                        entity.setId(existing.getId());
                        entity.setCreateTime(existing.getCreateTime()); // 保留原创建时间
                    });

            /* -------- 确保详情的businessId正确设置 -------- */
            entity.getPatrolDetailList().forEach(detail -> {
                detail.setBusinessId(entity.getRecordId());
            });

            /* -------- 保存聚合根（JPA自动处理详情的先删后插） -------- */
            PatrolRecord saved = patrolRecordDao.save(entity);
            return saved.getId().toString();

        } catch (Exception e) {
            throw new RuntimeException("保存巡店记录聚合失败：" + e.getMessage(), e);
        }
    }

    /**
     * 批量幂等保存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveAll(List<PatrolRecord> entities) {
        List<String> ids = new ArrayList<>(entities.size());
        for (PatrolRecord e : entities) {
            ids.add(save(e));
        }
        return ids;
    }

    /**
     * 根据时间范围查找巡店记录聚合（只查询未删除的记录）
     * JPA会自动级联加载PatrolDetail
     */
    @Override
    public List<PatrolRecord> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return List.of();
        }

        return patrolRecordDao.findByBizCreateTimeBetweenAndDelFlag(startTime, endTime, NORMAL_FLAG);
    }

    /**
     * 检查业务记录ID是否存在（只检查未删除的记录）
     */
    @Override
    public boolean existsByRecordId(Long recordId) {
        if (recordId == null) {
            return false;
        }
        return patrolRecordDao.findByRecordIdAndDelFlag(recordId, NORMAL_FLAG).isPresent();
    }

    /**
     * 根据业务记录ID删除巡店记录（软删除）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteByRecordId(Long recordId) {
        try {
            // 查找记录时不限制删除标识，因为可能需要删除已存在的记录
            Optional<PatrolRecord> entityOpt = patrolRecordDao.findByRecordId(recordId);
            if (entityOpt.isEmpty()) {
                throw new RuntimeException("巡店记录不存在，recordId：" + recordId);
            }

            PatrolRecord entity = entityOpt.get();
            entity.setDelFlag(DELETED_FLAG);
            PatrolRecord saved = patrolRecordDao.save(entity);
            return saved.getId().toString();
        } catch (Exception e) {
            throw new RuntimeException("根据recordId删除巡店记录失败：" + e.getMessage(), e);
        }
    }

    /**
     * 批量删除巡店记录（软删除）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<PatrolRecord> entities) {
        try {
            entities.forEach(entity -> entity.setDelFlag(DELETED_FLAG));
            patrolRecordDao.saveAll(entities);
        } catch (Exception e) {
            throw new RuntimeException("批量删除巡店记录失败：" + e.getMessage(), e);
        }
    }
}
