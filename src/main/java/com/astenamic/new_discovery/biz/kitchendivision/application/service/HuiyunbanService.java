package com.astenamic.new_discovery.biz.kitchendivision.application.service;

import com.alibaba.excel.EasyExcel;
import com.astenamic.new_discovery.biz.kitchendivision.application.service.dto.MaterialHourDto;
import com.astenamic.new_discovery.biz.kitchendivision.application.service.dto.WeekDatesClassification;
import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.DivisionDetail;
import com.astenamic.new_discovery.common.util.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class HuiyunbanService {

    private final DivisionDetailAppService divisionDetailAppService;


    public void exportData(String titleName, String lowMinstId, String highMinstId) {
        // 1. 查询业务数据
        List<MaterialHourDto> allData = buildData(lowMinstId, highMinstId);

        // 2. 输出路径（改为 .xlsx）
        String outPath = "D:/excel/" + titleName + ".xlsx";
        File outFile = new File(outPath);
        File parent = outFile.getParentFile();
        if (parent != null && !parent.exists() && !parent.mkdirs()) {
            System.err.println("目录创建失败: " + parent.getAbsolutePath());
            return;
        }

        // 3. 模板 + 写文件
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try (InputStream template = resolver
                .getResource("classpath:templates/原材料导入模版.xlsx")
                .getInputStream()) {

            // 官方推荐链式写法，无需手动 writer.finish()
            EasyExcel.write(outPath)
                    .withTemplate(template)
                    .sheet()        // 默认第 1 个 sheet
                    .doFill(allData);

        } catch (Exception e) {
            // 建议至少打印日志或向上抛
            e.printStackTrace();
        }
    }


    public List<MaterialHourDto> buildData(String lowMinstId, String highMinstId) {
        /**
         * 低位日
         */
        List<DivisionDetail> lowDivisionDetails = divisionDetailAppService.getDivisionDetailByMinstId(lowMinstId);

        /**
         * 高位日
         */
        List<DivisionDetail> highDivisionDetails = divisionDetailAppService.getDivisionDetailByMinstId(highMinstId);

        // 获取低位日MaterialHourDto列表
        List<MaterialHourDto> lowDays = generateMaterialHourDtoList(lowDivisionDetails);

        // 优化中位日数据获取：创建新的对象而不是修改原对象
        List<MaterialHourDto> medianDays = createMedianDays(lowDays);

        // 获取高位日数据列表
        List<MaterialHourDto> highDays = generateMaterialHourDtoList(highDivisionDetails);

        // 获取下周的日期分类
        WeekDatesClassification weekDates = getWeekDatesClassification();

        // 根据日期分类分配不同的数据
        List<MaterialHourDto> allData = new ArrayList<>();

        // 低位日数据（周一到周四）
        allData.addAll(duplicateMaterialHourDtosByDates(lowDays, weekDates.getLowDays()));

        // 中位日数据（周五）
        allData.addAll(duplicateMaterialHourDtosByDates(medianDays, weekDates.getMedianDays()));

        // 高位日数据（周末）
        allData.addAll(duplicateMaterialHourDtosByDates(highDays, weekDates.getHighDays()));

        log.info("构建完成 - 低位日: {} 条, 中位日: {} 条, 高位日: {} 条, 总计: {} 条",
                lowDays.size() * weekDates.getLowDays().size(),
                medianDays.size() * weekDates.getMedianDays().size(),
                highDays.size() * weekDates.getHighDays().size(),
                allData.size());

        // 根据name、startDate、endDate进行重复过滤
        List<MaterialHourDto> filteredData = removeDuplicates(allData);

        log.info("重复过滤后数据量: {} 条（过滤掉 {} 条重复数据）",
                filteredData.size(), allData.size() - filteredData.size());

        return filteredData;
    }

    /**
     * 根据name、startDate、endDate进行重复过滤
     *
     * @param allData 原始数据列表
     * @return 去重后的数据列表
     */
    private List<MaterialHourDto> removeDuplicates(List<MaterialHourDto> allData) {
        Map<String, MaterialHourDto> uniqueMap = new LinkedHashMap<>();
        List<MaterialHourDto> duplicates = new ArrayList<>();

        for (MaterialHourDto dto : allData) {
            String key = createUniqueKey(dto.getName(), dto.getStartDate(), dto.getEndDate());

            if (uniqueMap.containsKey(key)) {
                // 发现重复数据
                MaterialHourDto existing = uniqueMap.get(key);

                // 检查unitName是否相同
                if (isSameUnitName(existing.getUnitName(), dto.getUnitName())) {
                    // unitName相同，合并备货量
                    Float existingQuantity = existing.getStockQuantity() != null ? existing.getStockQuantity() : 0f;
                    Float newQuantity = dto.getStockQuantity() != null ? dto.getStockQuantity() : 0f;
                    Float mergedQuantity = existingQuantity + newQuantity;
                    existing.setStockQuantity(mergedQuantity);

                    log.info("合并重复数据 - Key: {}, 原备货量: {}, 新备货量: {}, 合并后: {}",
                            key, existingQuantity, newQuantity, mergedQuantity);
                } else {
                    // unitName不同，记录为重复数据但不合并
                    duplicates.add(dto);
                    log.warn("发现重复数据但unitName不同 - Key: {}", key);
                    log.warn("  原数据: [name={}, startDate={}, endDate={}, stockQuantity={}, unitName={}]",
                            existing.getName(), existing.getStartDate(), existing.getEndDate(),
                            existing.getStockQuantity(), existing.getUnitName());
                    log.warn("  重复数据: [name={}, startDate={}, endDate={}, stockQuantity={}, unitName={}]",
                            dto.getName(), dto.getStartDate(), dto.getEndDate(),
                            dto.getStockQuantity(), dto.getUnitName());
                }
            } else {
                // 第一次出现，保存
                uniqueMap.put(key, dto);
            }
        }

        if (!duplicates.isEmpty()) {
            log.info("总共发现 {} 条重复数据", duplicates.size());
        } else {
            log.info("未发现重复数据");
        }

        return new ArrayList<>(uniqueMap.values());
    }

    /**
     * 检查两个unitName是否相同
     */
    private boolean isSameUnitName(String unitName1, String unitName2) {
        if (unitName1 == null && unitName2 == null) {
            return true;
        }
        if (unitName1 == null || unitName2 == null) {
            return false;
        }
        return unitName1.equals(unitName2);
    }

    /**
     * 创建唯一键
     */
    private String createUniqueKey(String name, String startDate, String endDate) {
        return String.format("%s|%s|%s",
                name != null ? name : "",
                startDate != null ? startDate : "",
                endDate != null ? endDate : "");
    }

    /**
     * 创建中位日数据（低位日数据乘以1.3）
     */
    private List<MaterialHourDto> createMedianDays(List<MaterialHourDto> lowDays) {
        return lowDays.stream()
                .map(this::createMedianDayFromLowDay)
                .collect(Collectors.toList());
    }

    /**
     * 从低位日数据创建中位日数据
     */
    private MaterialHourDto createMedianDayFromLowDay(MaterialHourDto lowDay) {
        MaterialHourDto medianDay = new MaterialHourDto();

        // 复制所有字段
        medianDay.setDate(lowDay.getDate());
        medianDay.setStartDate(lowDay.getStartDate());
        medianDay.setEndDate(lowDay.getEndDate());
        medianDay.setName(lowDay.getName());
        medianDay.setUnitName(lowDay.getUnitName());

        // 备货量乘以1.3
        if (lowDay.getStockQuantity() != null) {
            medianDay.setStockQuantity(lowDay.getStockQuantity() * 1.3f);
        }

        return medianDay;
    }

    /**
     * 获取一周日期分类
     */
    private WeekDatesClassification getWeekDatesClassification() {
        LocalDateTime now = LocalDateTime.now().plusDays(7);
        LocalDate[] weekRange = TimeUtils.getWeekDateRange(now, 0);

        List<String> lowDays = new ArrayList<>();    // 周一到周四
        List<String> medianDays = new ArrayList<>(); // 周五
        List<String> highDays = new ArrayList<>();   // 周末（周六、周日）

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (LocalDate date = weekRange[0]; date.isBefore(weekRange[1]); date = date.plusDays(1)) {
            String dateStr = date.format(formatter);
            int dayOfWeek = date.getDayOfWeek().getValue(); // 1=周一, 7=周日

            if (dayOfWeek >= 1 && dayOfWeek <= 4) {
                // 周一到周四：低位日
                lowDays.add(dateStr);
            } else if (dayOfWeek == 5) {
                // 周五：中位日
                medianDays.add(dateStr);
            } else {
                // 周六、周日：高位日
                highDays.add(dateStr);
            }
        }

        return new WeekDatesClassification(lowDays, medianDays, highDays);
    }

    private List<String> getNextWeekDates() {
        List<String> nextWeekDates = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        LocalDate[] weekRange = TimeUtils.getWeekDateRange(now, 0);
        for (LocalDate date = weekRange[0]; date.isBefore(weekRange[1]); date = date.plusDays(1)) {
            nextWeekDates.add(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        return nextWeekDates;
    }

    /**
     * 根据日期列表重复生成MaterialHourDto数据
     *
     * @param originalDtos 原始MaterialHourDto列表
     * @param dates        日期列表
     * @return 重复生成的MaterialHourDto列表
     */
    private List<MaterialHourDto> duplicateMaterialHourDtosByDates(List<MaterialHourDto> originalDtos, List<String> dates) {
        List<MaterialHourDto> duplicatedDtos = new ArrayList<>();

        if (originalDtos == null || originalDtos.isEmpty() || dates == null || dates.isEmpty()) {
            log.warn("原始MaterialHourDto列表或日期列表为空，无法重复生成数据");
            return duplicatedDtos;
        }

        // 为每个日期复制一份MaterialHourDto数据
        for (String date : dates) {
            for (MaterialHourDto originalDto : originalDtos) {
                MaterialHourDto duplicatedDto = createDuplicatedMaterialHourDto(originalDto, date);
                duplicatedDtos.add(duplicatedDto);
            }
        }

        log.debug("原始数据 {} 条，日期 {} 个，重复生成 {} 条数据",
                originalDtos.size(), dates.size(), duplicatedDtos.size());

        return duplicatedDtos;
    }

    /**
     * 创建重复的MaterialHourDto，设置指定日期
     *
     * @param original 原始MaterialHourDto
     * @param date     指定日期
     * @return 新的MaterialHourDto
     */
    private MaterialHourDto createDuplicatedMaterialHourDto(MaterialHourDto original, String date) {
        MaterialHourDto duplicated = new MaterialHourDto();

        // 复制所有字段
        duplicated.setDate(date);  // 设置新的日期
        duplicated.setStartDate(date + " " + original.getStartDate());
        duplicated.setEndDate(date + " " + original.getEndDate());
        duplicated.setName(original.getName());
        duplicated.setStockQuantity(original.getStockQuantity());
        duplicated.setUnitName(original.getUnitName());

        return duplicated;
    }

    /**
     * 根据分工明细列表生成MaterialHourDto列表
     *
     * @param divisionDetails 分工明细列表
     * @return MaterialHourDto列表
     */
    public List<MaterialHourDto> generateMaterialHourDtoList(List<DivisionDetail> divisionDetails) {
        List<MaterialHourDto> allMaterialHourDtos = new ArrayList<>();

        if (divisionDetails == null || divisionDetails.isEmpty()) {
            log.warn("分工明细列表为空，无法生成MaterialHourDto");
            return allMaterialHourDtos;
        }

        for (DivisionDetail divisionDetail : divisionDetails) {
            try {
                List<MaterialHourDto> materialHourDtos = divisionDetailAppService.generateMaterialHourDtoList(divisionDetail);
                allMaterialHourDtos.addAll(materialHourDtos);
                log.debug("分工明细 {} 生成了 {} 条MaterialHourDto", divisionDetail.getObjectId(), materialHourDtos.size());
            } catch (Exception e) {
                log.error("处理分工明细 {} 时发生错误: {}", divisionDetail.getObjectId(), e.getMessage(), e);
            }
        }

        return allMaterialHourDtos;
    }
}
