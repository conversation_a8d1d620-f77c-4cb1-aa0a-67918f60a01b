package com.astenamic.new_discovery.biz.flow.warning.service;


import com.astenamic.new_discovery.biz.flow.warning.mapper.SupplierPriceItemMapper;
import com.astenamic.new_discovery.biz.purchase.domain.support.enums.PurchaseStatus;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.api.SupplierPricingSession;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.SupplierPriceDTO;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.SupplierPriceItemDTO;
import com.astenamic.new_discovery.biz.flow.warning.entity.AbnormalPrice;
import com.astenamic.new_discovery.common.util.TimeUtils;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AbnormalPriceService {

    private static final Logger logger = LoggerFactory.getLogger(AbnormalPriceService.class);

    private final YiDaSession yiDaSession;

    private final SupplierPricingSession supplierPricingSession;

    private final SupplierPriceItemMapper supplierPriceItemMapper;

    /**
     * 检测成本单价异常波动
     *
     * @param target 审核时间
     */
    public void detectAbnormalPriceFluctuation(LocalDateTime target) {
        target = target.with(LocalTime.MIN);

        long[] time = TimeUtils.getDayRange(target);

        SearchConditions cond = SearchCondition
                .builder()
                .dateBetween("dateField_m96vf8dv", time, "+")
                .get();
        int page = 1;
        int size = 0;

        List<AbnormalPrice> oldData = new ArrayList<>();
        do {
            List<AbnormalPrice> od = new ArrayList<>();
            try {
                od = this.yiDaSession.searchFormDataConditionsRequest(AbnormalPrice.class, cond, page);
            } catch (Exception e) {
                logger.error("获取宜搭报表数据失败，{}{}", e.getMessage(), System.lineSeparator(), e);
                throw e;
            }
            page++;
            size = od.size();
            oldData.addAll(od);
        } while (size == 100);

        List<SupplierPriceDTO> priceList = supplierPricingSession.getPriceList(100, target.toLocalDate(), target.toLocalDate(), PurchaseStatus.COMPLETED.getCode());
        List<AbnormalPrice> newData = deduplicate(
                priceList.stream()
                        .filter(sheet -> sheet.getStatus().equals("-2"))
                        .flatMap(sheet -> sheet.getItems().stream()
                                .peek(item -> {
                                    item.setCtime(sheet.getCtime());
                                    item.setAtime(sheet.getAtime());
                                })
                        ).toList()
        ).stream()
                .filter(this::filterByDate)
                .toList();

        for (AbnormalPrice oldDatum : oldData) {
            for (AbnormalPrice newDatum : newData) {
                if (oldDatum.getGood_sno().equals(newDatum.getGood_sno()) && oldDatum.getApplyguname().equals(newDatum.getApplyguname())) {
                    newDatum.setInstanceId(oldDatum.getInstanceId());
                    break;
                }
            }
        }

        Map<Integer, List<AbnormalPrice>> data = newData.stream()
                .collect(Collectors.groupingBy(d -> StringUtils.isBlank(d.getInstanceId()) ? 1 : 2));

        data.forEach((k, rows) -> {
            for (AbnormalPrice group : rows) {
                if (k == 1) {
                    this.yiDaSession.processSave(group, AbnormalPrice.class, "", "75603689");
                } else {
                    // Todo: 流程表单不允许更新
                }
            }
        });

    }

    /**
     * 物料去重：取波动最大的一条
     *
     * @param yiDaBaseList 物料列表
     * @return 物料列表
     */
    public List<AbnormalPrice> deduplicate(List<SupplierPriceItemDTO> yiDaBaseList) {
        Map<String, AbnormalPrice> resultMap = new HashMap<>();
        for (SupplierPriceItemDTO item : yiDaBaseList) {
            AbnormalPrice abnormalPrice = supplierPriceItemMapper.toAbnormalPrice(item);
            Float momValue = abnormalPrice.getMomratio();
            if (momValue == null) {
                continue;
            }
            String compositeKey = item.getGood_sno() + "_" + item.getApplyguname();
            if (!resultMap.containsKey(compositeKey)) {
                resultMap.put(compositeKey, abnormalPrice);
            } else {
                Float existingValue = resultMap.get(compositeKey).getMomratio();
                if (existingValue == null || momValue > existingValue) {
                    resultMap.put(compositeKey, abnormalPrice);
                }
            }
        }
        return new ArrayList<>(resultMap.values());
    }

    /**
     * 过滤异常波动数据
     *
     * @param abnormalPrice 异常波动数据
     * @return 是否符合条件
     */
    private boolean filterByDate(AbnormalPrice abnormalPrice) {
        if (abnormalPrice == null) {
            return false;
        }

        String goodType = abnormalPrice.getGoodtype_name();
        Float momratio = abnormalPrice.getMomratio();

        if (StringUtils.isBlank(goodType) || momratio == null) {
            return false;
        }

        if (goodType.contains("生鲜蔬菜类") || goodType.contains("水果类")) {
            return momratio > 40 || momratio < -40;
        } else {
            return momratio > 20 || momratio < -20;
        }
    }

    /**
     * 解析字符串为 Float
     *
     * @param momratioStr momratio 字符串
     * @return 解析后的 Float 值，或 null 如果解析失败
     */
    private Float parseMomratio(String momratioStr) {
        if (momratioStr == null || momratioStr.trim().isEmpty()) {
            return null;
        }
        // 移除可能存在的 "%" 符号，并修剪空格
        String cleaned = momratioStr.replace("%", "").trim();
        try {
            return Float.parseFloat(cleaned);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
