package com.astenamic.new_discovery.biz.flow.warning.mapper;

import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.SupplierPriceItemDTO;
import com.astenamic.new_discovery.biz.flow.warning.entity.AbnormalPrice;
import com.astenamic.new_discovery.common.util.MapperUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = MapperUtil.class)
public interface SupplierPriceItemMapper {

    @Mapping(source = "good_name", target = "good_name")
    @Mapping(source = "good_sno", target = "good_sno")
    @Mapping(source = "good_brand", target = "good_brand")
    @Mapping(source = "std", target = "std")
    @Mapping(source = "goodtype_name", target = "goodtype_name")
    @Mapping(source = "fgoodtype_name", target = "fgoodtype_name")
    @Mapping(source = "applyguname", target = "applyguname")
    @Mapping(source = "nowprice", target = "nowprice", qualifiedByName = "stringToFloat")
    @Mapping(source = "uprice", target = "uprice", qualifiedByName = "bigDecimalToFloat")
    @Mapping(source = "startdate", target = "startdate", qualifiedByName = "localDateToLocalDateTime")
    @Mapping(source = "enddate", target = "enddate", qualifiedByName = "localDateToLocalDateTime")
    @Mapping(source = "momratio", target = "momratio", qualifiedByName = "stringToFloat")
    @Mapping(source = "yoyratio", target = "yoyratio", qualifiedByName = "stringToFloat")
    @Mapping(source = "atime", target = "atime")
    AbnormalPrice toAbnormalPrice(SupplierPriceItemDTO dto);

}
