package com.astenamic.new_discovery.biz.purchase.application.command;

import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.CreatePricingReviewReq;
import com.astenamic.new_discovery.biz.purchase.application.mapper.SourceAuthMapper;
import com.astenamic.new_discovery.biz.purchase.application.mapper.SupplierQuotationMapper;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.SourceAuthentication;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.*;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.enums.PricingType;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.ProductConfirmationItem;
import com.astenamic.new_discovery.biz.purchase.domain.support.repository.dto.DispatchOutSummaryDTO;
import com.astenamic.new_discovery.biz.purchase.domain.support.repository.dto.StockSummaryDTO;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.util.*;

/**
 * 开始定价评审命令
 */
@Component
@RequiredArgsConstructor
public class CreatePricingReviewCmd implements
        PricingCommand<CreatePricingReviewReq, List<PricingReview>> {

    private static final Logger logger = LoggerFactory.getLogger(CreatePricingReviewCmd.class);

    private final ProductPricingContext ctx;

    private final SourceAuthMapper sourceAuthMapper;

    private final SupplierQuotationMapper supplierQuotationMapper;

    @Override
    @DingTalkAlert(title = "开始定价评审", atMobiles = {"19353308172"})
    public List<PricingReview> execute(CreatePricingReviewReq req) {

        String pricingType = req.pricingType();
        String pricingOrderNumber = req.pricingOrderNumber();
        String requirementCode = req.requirementCode();

        if (StringUtils.isEmpty(pricingOrderNumber)) {
            logger.warn("创建定价评审表单失败：参数不能为空");
            throw new BizException(Result.ErrorCode.VALIDATION_FAILED);
        }

        ProductPricingFlow pricingFlow = ctx.getProductPricingFlowService().getByPricingOrderNumber(pricingOrderNumber);

        List<ProductConfirmationItem> confirmationList = pricingFlow.getProductConfirmationList();

        List<PricingReview> reviews = new ArrayList<>();
        if (PricingType.NEW_PRODUCT.getDesc().equals(pricingType) && !"否".equals(pricingFlow.getIsNewProduct())) {
            /*
              1、查询寻源认证
             */
            List<SourceAuthentication> confirmedAuths =
                    ctx.getSourceAuthService()
                            .getByRequirementCode(requirementCode).stream()
                            .filter(a -> "审批通过".equals(a.getStatus()))
                            .toList();
            if (confirmedAuths.isEmpty()) {
                logger.warn("未找到寻源认证，需求单号：{}", requirementCode);
                throw new BizException("未找到寻源认证");
            }
            reviews = sourceAuthMapper.toReviewList(confirmedAuths, pricingOrderNumber);

            try {
                String joinedDesc = confirmedAuths.stream()
                        .map(SourceAuthentication::getRemarks)
                        .filter(Objects::nonNull)
                        .reduce((a, b) -> a + ", " + b)
                        .orElse("");
                pricingFlow.setDescription(joinedDesc);
                ctx.getProductPricingFlowService().updateFlow(pricingFlow);
            } catch (Exception e) {
                logger.error("更新物流定价流程失败：{}", e.getMessage(), e);
            }

        } else  {
            List<ProductSupplierQuotationFlow> sqFlows =
                    ctx.getProductSupplierQuotationFlowService()
                            .getFlowsByPricingOrderNumber(pricingOrderNumber);
            if (sqFlows.isEmpty()) {
                logger.warn("未找到供应商报价流程，定价单号：{}", pricingOrderNumber);
                throw new BizException("未找到供应商报价流程");
            }
            reviews = sqFlows.stream()
                    .flatMap(f -> supplierQuotationMapper
                            .toPricingReviewList(f, pricingOrderNumber)
                            .stream())
                    .toList();
        }

        /* 仓库装饰 + 辅助字段补充 */
        List<PricingReview> decorated = ctx.getWarehouseDecorator().decorate(reviews);
        decorated.forEach(item -> {
            ProductConfirmationItem pci = confirmationList.stream().filter(d -> d.getItemCode().equals(item.getProductCode())).findAny().orElse(null);
            if (pci != null) {
                item.setHistoricalPurchasePrice(pci.getCurrentPrice());
                item.setLastEndDate(pci.getEndDate());
            }
            if (StringUtils.isNotBlank(item.getProductCode())
                    && StringUtils.isNotBlank(item.getExecuteStoreCode())) {

                DispatchOutSummaryDTO dispatch =
                        ctx.getDailyDispatchProductSummaryRepository()
                                .findDispatchOutDetail(item.getExecuteStoreCode(),
                                        item.getProductCode());
                if (dispatch != null) {
                    float monthlyUsage = dispatch.getQty()
                            .setScale(2, RoundingMode.HALF_UP)
                            .floatValue();
                    item.setMonthlyUsage(monthlyUsage);

                    Float price = item.getPrice();
                    item.setEstimatedMonthlyPurchase(
                            price == null ? 0f : price * monthlyUsage);
                }

                StockSummaryDTO stock =
                        ctx.getMaterialStockRepository()
                                .findStockSummary(item.getExecuteStoreCode(),
                                        item.getProductCode());
                if (stock != null) {
                    item.setStock(stock.getStock());
                }
            }
        });

        try {
            ctx.getPricingReviewService().batchSave(decorated);
        } catch (Exception e) {
            logger.error("批量保存供应商报价流程失败：{}", e.getMessage(), e);
        }
        return decorated;
    }

}
