package com.astenamic.new_discovery.biz.purchase.application.command;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.ImportPricingReviewReq;
import com.astenamic.new_discovery.biz.purchase.application.excel.MergeAwarePricingListener;
import com.astenamic.new_discovery.biz.purchase.application.excel.StringToFloatConverter;
import com.astenamic.new_discovery.biz.purchase.application.service.dto.PricingReviewTemplateDTO;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.PricingReview;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.util.TimeUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;


/**
 * 导入定价评审表单命令
 * 1. 根据 URL 导入定价评审表单
 */
@Component
@RequiredArgsConstructor
public class ImportPricingReviewCmd implements
        PricingCommand<ImportPricingReviewReq, Void> {

    private static final Logger logger = LoggerFactory.getLogger(ImportPricingReviewCmd.class);
    private final ProductPricingContext ctx;

    @Override
    @DingTalkAlert(title = "导入定价评审表", atMobiles = {"19353308172"})
    public Void execute(ImportPricingReviewReq req) {

        String url = req.url();
        String pricingOrderNumber = req.pricingOrderNumber();

        try {
            byte[] data = IOUtils.toByteArray(download(
                    ctx.getYiDaSession().temporaryUrls(url, ProductPricingFlow.class)));


            /* 读取主体 */
            List<PricingReviewTemplateDTO> items;
            try (InputStream is = new ByteArrayInputStream(data)) {

                MergeAwarePricingListener listener = new MergeAwarePricingListener(2, "说明");
                EasyExcel.read(is, PricingReviewTemplateDTO.class, listener)
                        .registerConverter(new StringToFloatConverter())
                        .extraRead(CellExtraTypeEnum.MERGE) // ★ 开启合并信息
                        .headRowNumber(2)
                        .sheet()
                        .doRead();

                // 解析结果直接取
                items = listener.getData();
            }

            /* 读取全部行（找底部说明） */
            List<Map<Integer, String>> allRows;
            try (InputStream is = new ByteArrayInputStream(data)) {
                allRows = EasyExcel.read(is)
                        .sheet()
                        .headRowNumber(0)
                        .doReadSync();
            }

            int headerRow = 2;
            Map<String, Integer> rowNumMap = new HashMap<>();
            for (int i = 0; i < items.size(); i++) {
                rowNumMap.put(items.get(i).getObjectId(), headerRow + 1 + i);
            }

            int footerStart = -1;
            for (int i = headerRow + 1; i < allRows.size(); i++) {
                if ("说明".equals(allRows.get(i).get(0))) {
                    footerStart = i;
                    break;
                }
            }
            if (footerStart < 0) throw new IllegalStateException("未找到底部“说明”行");

            int descCol = 4;
            String decisionNote = allRows.get(footerStart + 1).getOrDefault(descCol, null);
            String approvalComments = allRows.get(footerStart + 3).getOrDefault(descCol, null);

            /* DTO → Entity */
            List<PricingReview> nowDatas =
                    ctx.getPricingReviewService().getByPricingOrderNumber(pricingOrderNumber);

            for (PricingReviewTemplateDTO dto : items) {
                PricingReview pr = nowDatas.stream()
                        .filter(p -> StringUtils.equals(p.getObjectId(), dto.getObjectId()))
                        .findFirst().orElse(null);
                if (pr != null) {
                    if (StringUtils.isEmpty(pr.getSupplierCode())) {
                        pr.setSupplierCode(
                                StringUtils.isEmpty(dto.getSupplierCode()) ? "" : dto.getSupplierCode());
                    }
                    pr.setSecondQuotation(dto.getSecondQuotation());
                    pr.setReductionAmount(dto.getReductionAmount());
                    pr.setFinalPrice(dto.getFinalPrice());
                    pr.setUnit(dto.getUnit());
                    pr.setCurrentCallQuantity(dto.getCurrentCallQuantity());
                    pr.setEnabled(dto.getEnabled());
                    LocalDateTime startDate = TimeUtils.toLocalDateTime(dto.getPriceStartDate());
                    LocalDateTime endDate = TimeUtils.toLocalDateTime(dto.getPriceEndDate());
                    pr.setPriceStartDate(startDate != null ? startDate.minusDays(1) : null);
                    pr.setPriceEndDate(endDate != null ? endDate.minusDays(1) : null);
                }
            }

            /* 基础校验 */
            for (PricingReview pr : nowDatas) {
                if (!"是".equals(pr.getEnabled())) continue;
                int rowNum = rowNumMap.getOrDefault(pr.getObjectId(), -1);
                if (StringUtils.isEmpty(pr.getExecuteStoreCode())
                        || StringUtils.isEmpty(pr.getSupplierCode())) {
                    throw new BizException("第" + rowNum + "行：执行门店或供应商为空");
                }
                if (pr.getFinalPrice() == null || pr.getFinalPrice() <= 0) {
                    throw new BizException("第" + rowNum + "行：最终报价无效");
                }
                try {
                    pr.validatePriceDates();
                } catch (Exception e) {
                    throw new BizException("第" + rowNum + "行：" + e.getMessage());
                }

            }

            ctx.getPricingReviewService().batchSave(nowDatas);

            ProductPricingFlow flow =
                    ctx.getProductPricingFlowService().getByPricingOrderNumber(pricingOrderNumber);
            flow.setDecisionNote(decisionNote);
            flow.setApprovalComments(approvalComments);
            ctx.getYiDaSession()
                    .batchUpdateDataByInstanceId(Collections.singletonList(flow),
                            ProductPricingFlow.class);

        } catch (Exception e) {
            logger.error("导入定价评审表失败：", e);
            throw new BizException("导入失败，" + e.getMessage());
        }
        return null;
    }

    /* ------------ helpers -------------- */
    private InputStream download(String useUrl) throws Exception {
        java.net.URL u = new java.net.URL(useUrl);
        java.net.HttpURLConnection c = (java.net.HttpURLConnection) u.openConnection();
        c.setRequestMethod("GET");
        c.setConnectTimeout(5000);
        c.setReadTimeout(5000);
        if (c.getResponseCode() != java.net.HttpURLConnection.HTTP_OK) {
            throw new RuntimeException("下载失败，HTTP " + c.getResponseCode());
        }
        return c.getInputStream();
    }
}
