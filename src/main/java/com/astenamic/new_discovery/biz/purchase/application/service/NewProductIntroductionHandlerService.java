package com.astenamic.new_discovery.biz.purchase.application.service;

import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.NewProductIntroductionFlow;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.SourceAuthentication;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.valueobject.SourceAuthItem;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.service.PricingReviewService;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.PricingReview;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.ProductConfirmationItem;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.ProductItem;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.service.NewProductIntroductionFlowService;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.service.ProductPricingFlowService;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.service.SourceAuthenticationService;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.service.SupplierAdmissionFlowService;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.SupplierInfo;
import com.astenamic.new_discovery.biz.purchase.domain.support.service.SupplierInfoService;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.form.FormDefinition;
import com.astenamic.new_discovery.form.manage.FormManager;
import com.astenamic.new_discovery.common.util.ThreadPoolUtils;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.modal.YidaAssociation;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 处理新品引入流程的领域服务
 */
@Service
@AllArgsConstructor
public class NewProductIntroductionHandlerService {

    private static final Logger logger = LoggerFactory.getLogger(NewProductIntroductionHandlerService.class);

    private final SupplierAdmissionFlowService supplierAdmissionFlowService;

    private final NewProductIntroductionFlowService introductionFlowService;

    private final SourceAuthenticationService sourceAuthService;

    private final ProductPricingFlowService productPricingFlowService;

    private final YiDaSession yiDaSession;

    private final YidaConfigProperties yidaConfigProperties;

    private final FormManager formManager;

    private final PricingReviewService pricingReviewService;

    private final SupplierInfoService supplierInfoService;

    /**
     * 新品触发定价流程
     */
    @DingTalkAlert(title = "新品触发定价流程", atMobiles = {"19353308172"})
    public void handleProductPricing(String requirementCode) {
        //1、查询新品引入流程
        NewProductIntroductionFlow newProductIntroductionFlow = introductionFlowService.getFlowByRequirementCode(requirementCode);
        if (newProductIntroductionFlow == null) {
            logger.warn("新品引入流程不存在，需求单号：{}", requirementCode);
            throw new BizException(Result.ErrorCode.VALIDATION_FAILED);
        }

        //2、查询货品定价流程
        ProductPricingFlow productPricingFlow = productPricingFlowService.getByRequirementCode(requirementCode);
        if (productPricingFlow == null) {
            logger.warn("货品定价流程不存在，需求单号：{}", requirementCode);
            throw new BizException(Result.ErrorCode.VALIDATION_FAILED);
        }

        ProductItem productItem = new ProductItem();
        productItem.setItemName(newProductIntroductionFlow.getProductName());
        productItem.setItemCode(newProductIntroductionFlow.getProductCode());
        productItem.setItemSpec(newProductIntroductionFlow.getDemandSpecificationOrModel());
        productItem.setItemCategory(newProductIntroductionFlow.getItemCategory().get(0).getTitle());
        productItem.setItemSubcategory(newProductIntroductionFlow.getItemSubcategory().get(0).getTitle());
        productItem.setOrderUnit(newProductIntroductionFlow.getDemandUnit());
        productPricingFlow.setProductTable(List.of(productItem));

        ProductConfirmationItem productConfirmationItem = new ProductConfirmationItem();
        BeanUtils.copyProperties(productItem, productConfirmationItem);
        productPricingFlow.setProductConfirmationList(List.of(productConfirmationItem));

        //3、更新货品定价流程
        productPricingFlowService.updateFlow(productPricingFlow);
    }

    /**
     * 同步寻源认证
     */
    @DingTalkAlert(title = "同步寻源认证", atMobiles = {"19353308172"})
    public void syncSourceAuthItem(String requirementCode) {
        // 获取新品引入流程实例
        NewProductIntroductionFlow newFlow = introductionFlowService.getFlowByRequirementCode(requirementCode);
        if (newFlow == null) {
            logger.error("未找到对应的新品引入流程，需求单号：{}", requirementCode);
            throw new BizException(Result.ErrorCode.FLOW_NOT_FOUND);
        }

        List<SourceAuthentication> authList = sourceAuthService.getByRequirementCode(requirementCode);

        if (authList.isEmpty()) {
            logger.error("未找到任何寻源认证，需求单号：{}", requirementCode);
        } else {
            List<SourceAuthItem> list = authList.stream().map(auth -> {
                SourceAuthItem sourceAuthItem = new SourceAuthItem();

                FormDefinition formDefinition = this.formManager.getFormDefinition(SourceAuthentication.class);
                YidaAssociation yidaAssociation = new YidaAssociation();
                yidaAssociation.setAppType(formDefinition.getAppType());
                yidaAssociation.setFormUuid(formDefinition.getCode());
                yidaAssociation.setInstanceId(auth.getObjectId());
                yidaAssociation.setTitle(auth.getItemName());

                sourceAuthItem.setSourceAuthentication(List.of(yidaAssociation));
                sourceAuthItem.setSourcingAgents(auth.getSourcingAgents() != null && !auth.getSourcingAgents().isEmpty() ? auth.getSourcingAgents().get(0) : "");
                sourceAuthItem.setSourceAuthCode(auth.getSourcingAuthCode());
                sourceAuthItem.setInitialQuote(auth.getInitialQuote());
                sourceAuthItem.setSupplierName(auth.getNewSupplierName());
                sourceAuthItem.setUnit(auth.getQuoteUnit());
                sourceAuthItem.setIsNewSupplier(auth.getIsNewSupplier());
                if ("否".equals(sourceAuthItem.getIsNewSupplier())) {
                    sourceAuthItem.setApprovalResult("审批通过");
                }
                return sourceAuthItem;
            }).toList();

            newFlow.setSourceAuthTable(list);

            ThreadPoolUtils.executeSingleTask(() -> {
                try {
                    yiDaSession.batchUpdateDataByInstanceId(List.of(newFlow), NewProductIntroductionFlow.class);
                } catch (Exception e) {
                    logger.error("批量保存寻源认证失败，错误信息：{}", e.getMessage(), e);
                }
            });
        }
    }

    /**
     * 同步供应商信息
     */
    @DingTalkAlert(title = "同步供应商信息", atMobiles = {"19353308172"})
    public void syncNewSupplierInfo(String requirementCode) {
        List<SupplierInfo> supplierInfos = new ArrayList<>();

        List<SourceAuthentication> authList = sourceAuthService.getByRequirementCode(requirementCode);
        for (SourceAuthentication authItem : authList) {
            if (!"是".equals(authItem.getIsNewSupplier())) {
                continue;
            }
            List<PricingReview> sourcingAuthCodes = pricingReviewService.getBySourcingAuthCode(authItem.getSourcingAuthCode());
            List<PricingReview> yesRecords = sourcingAuthCodes.stream()
                    .filter(pr -> "是".equalsIgnoreCase(pr.getEnabled()))
                    .toList();
            if (yesRecords.isEmpty()) {
                continue;
            }
            SupplierInfo supplierInfo = new SupplierInfo();
            supplierInfo.setName(authItem.getNewSupplierName());
            supplierInfo.setSno(yesRecords.get(0).getSupplierCode());
            supplierInfo.setContact(authItem.getSupplierContactPerson());
            supplierInfo.setInvoicetype(authItem.getInvoiceType());
            supplierInfo.setAccountdatetype(authItem.getSettlementMethod());
            supplierInfo.setMobile(authItem.getSupplierContact());
            supplierInfo.setRegisteredCapital(authItem.getRegisteredCapital());
            supplierInfo.setAnnualTotalSales(authItem.getAnnualSales());
            supplierInfo.setValid("有效");
            supplierInfo.setExistingCooperativeUnits(authItem.getExistingPartnerBrand());
            supplierInfo.setBusinessLicenseFiles(authItem.getBusinessLicenseAttachment());
            supplierInfo.setFoodBusinessLicenseFiles(authItem.getFoodBusinessLicenseAttachment());
            supplierInfo.setFoodProductionLicenseFiles(authItem.getFoodProductionLicenseAttachment());
            supplierInfo.setFoodProductionLicenseFilesUpdateTime(authItem.getFoodProductionLicenseExpiry());
            supplierInfo.setOtherFiles(authItem.getOtherDocuments());
            supplierInfo.setApplyPeople(authItem.getSourcingAgents().get(0));
            supplierInfos.add(supplierInfo);
        }

        if (!supplierInfos.isEmpty()) {
            supplierInfoService.batchSave(supplierInfos);
        }

    }


}
