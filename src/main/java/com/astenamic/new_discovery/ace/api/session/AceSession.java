package com.astenamic.new_discovery.ace.api.session;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.astenamic.new_discovery.ace.api.config.AceConfiguration;
import com.astenamic.new_discovery.ace.api.http.response.ArrayDataBody;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.common.util.TimeUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public abstract class AceSession {
    @Autowired
    private AceConfiguration config;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ObjectMapper objectMapper;

    protected <T> List<T> getListRequest(String resource, JSONObject params, Class<T> tClass) {

        this.signParams(params);

        String url = this.getUrl(resource);

        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);

        params.forEach(urlBuilder::queryParam);

        URI uri = urlBuilder.build().toUri();

        ArrayDataBody response = this.restTemplate.getForObject(uri, ArrayDataBody.class);

        if (response == null) {
            throw new IllegalStateException("请求失败，响应体为空");
        }

        boolean success = response.getSuccess();

        if (!success) {
            throw new IllegalStateException("请求失败，错误信息：" + response.getMsg());
        }

        List<JSONObject> dataArr = response.getData();

        if (dataArr == null) {
            return null;
        }

        return dataArr.stream().map(j -> j.to(tClass, JSONReader.Feature.FieldBased)).toList();
    }

    protected JSONObject signPostParams(JSONObject params) {
        JSONObject p = new JSONObject();
        p.put("appkey", this.config.getAppKey());
        p.put("data", params.toJSONString());
        long timestamp = TimeUtils.toEpochMilli(LocalDateTime.now());
        p.put("front", timestamp);
        // 根据 key 升序排序
        List<String> strArr2 = new ArrayList<>();

        for (Map.Entry<String, Object> entry : p.entrySet()) {
            strArr2.add(entry.getKey() + "=" + entry.getValue());
        }

        Collections.sort(strArr2);

        // 把数组转换成字符串，拼接成 a=1&b=2 这样的字符串
        String str = String.join("&", strArr2);

        // 计算签名
        String sign = md5(str + this.config.getAppSecret());

        p.put("sign", sign);

        return p;
    }

    protected void signParams(JSONObject params) {

        String parStr = params.toJSONString();

        params.put("appkey", this.config.getAppKey());
        params.put("data", parStr);
        long timestamp = TimeUtils.toEpochMilli(LocalDateTime.now());
        params.put("front", timestamp);

        // 根据 key 升序排序
        List<String> strArr2 = new ArrayList<>();

        for (Map.Entry<String, Object> entry : params.entrySet()) {
            strArr2.add(entry.getKey() + "=" + entry.getValue());
        }

        Collections.sort(strArr2);

        // 把数组转换成字符串，拼接成 a=1&b=2 这样的字符串
        String str = String.join("&", strArr2);

        // 计算签名
        String sign = md5(str + this.config.getAppSecret());

        params.put("sign", sign);
    }

    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    protected String getUrl(String uri) {
        return this.config.getBaseUrl() + uri;
    }

    /**
     * @param resource    业务地址片段，例如 "/purchase/api/price/create"
     * @param requestBody 请求 DTO
     * @param respClass   需要反序列化成的返回类型；若接口直接返回列表，可传 YourDTO[].class
     * @param <T>         请求体泛型
     * @param <R>         响应体泛型
     */
    protected <T, R> R postRequest(String resource, T requestBody, Class<R> respClass) {

        // 1. 序列化请求对象 => JSONObject
        JSONObject params = JSONObject.parseObject(JSON.toJSONString(requestBody));

        // 2. 加签
        JSONObject signedParams = this.signPostParams(params);

        // 3. 发送 POST
        String url = this.getUrl(resource);
        ResponseEntity<String> entity = restTemplate
                .postForEntity(url, signedParams, String.class);

        if (!entity.getStatusCode().is2xxSuccessful()
                || entity.getBody() == null) {
            log.error("请求失败: {}", entity);
            throw new BizException(Result.ErrorCode.REQUEST_ERROR);
        }

        // 4. 把返回体先解析成 JSONObject，做统一业务校验
        JSONObject body = JSON.parseObject(entity.getBody());

        // -------- 5. 统一反序列化 --------
        return deserialize(body, respClass);
    }

    protected <R> R deserialize(Object payload, Class<R> respClass) {
        try {
            String json = JSON.toJSONString(payload);
            // 普通对象
            return JSON.parseObject(
                    json,
                    respClass,
                    JSONReader.Feature.FieldBased,
                    JSONReader.Feature.SupportSmartMatch);
        } catch (Exception e) {
            log.error("反序列化失败: {}", e.getMessage(), e);
            throw new BizException(Result.ErrorCode.SERVER_ERROR);
        }
    }
}
