package com.astenamic.new_discovery.common.util;

import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class EasyExcelUtil {

    /**
     * 初始化响应体
     *
     * @param response 请求头
     * @param fileName 导出名称
     */
    public static void initResponse(HttpServletResponse response, String fileName) {

        String finalFileName = fileName + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        finalFileName = URLEncoder.encode(finalFileName, StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + finalFileName + ".xlsx");

    }
}
