package com.astenamic.new_discovery.common.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

public class TimeUtils {

    private static final ZoneId ZONE = ZoneId.of("Asia/Shanghai");

    private static final DateTimeFormatter FLEXIBLE_DATE_TIME_PARSER = new DateTimeFormatterBuilder()
            // —— 分支1：紧凑型 yyyyMMddHHmmss ——
            .optionalStart()
            .appendPattern("yyyyMMddHHmmss")
            .optionalEnd()
            // —— 分支2：带分隔符的年-月-日[ 时:分[:秒]] ——
            .optionalStart()
            // 年
            .appendValue(ChronoField.YEAR, 4)
            .appendLiteral('-')
            // 月（1或2位）
            .appendValue(ChronoField.MONTH_OF_YEAR)
            .appendLiteral('-')
            // 日（1或2位）
            .appendValue(ChronoField.DAY_OF_MONTH)
            // 可选：空格 + 时:分
            .optionalStart()
            .appendLiteral(' ')
            .appendValue(ChronoField.HOUR_OF_DAY)
            .appendLiteral(':')
            .appendValue(ChronoField.MINUTE_OF_HOUR)
            // 可选：:秒
            .optionalStart()
            .appendLiteral(':')
            .appendValue(ChronoField.SECOND_OF_MINUTE)
            .optionalEnd()
            .optionalEnd()
            .optionalEnd()
            // —— 缺失的时分秒默认设为 0 ——
            .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
            .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
            .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
            .toFormatter();


    /**
     * 获取一天（00:00～23:59:59.999）的时间戳范围
     */
    public static long[] getDayRange(LocalDateTime dt) {
        LocalDate d = dt.toLocalDate();
        long start = d.atStartOfDay(ZONE).toInstant().toEpochMilli();
        long end = d.plusDays(1).atStartOfDay(ZONE).toInstant().toEpochMilli() - 1;
        return new long[]{start, end};
    }

    /**
     * 获取一周（周一00:00～下周一00:00前1毫秒）的时间戳范围
     */
    public static long[] getWeekRange(LocalDateTime dt) {
        LocalDate d = dt.toLocalDate();
        LocalDate monday = d.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate nextMonday = monday.plusWeeks(1);
        long start = monday.atStartOfDay(ZONE).toInstant().toEpochMilli();
        long end = nextMonday.atStartOfDay(ZONE).toInstant().toEpochMilli() - 1;
        return new long[]{start, end};
    }

    /**
     * 获取一周内任意起止日（如周二到下周一）的时间戳范围
     *
     * @param dt          基准时间
     * @param startOffset 起始日相对本周一的偏移（0=周一，1=周二，-1=上周日等）
     * @return [start, end] 毫秒时间戳
     */
    public static long[] getWeekRange(LocalDateTime dt, int startOffset) {
        LocalDate baseMonday = dt.toLocalDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate startDay = baseMonday.plusDays(startOffset);
        LocalDate nextMonday = baseMonday.plusWeeks(1);
        long start = startDay.atStartOfDay(ZONE).toInstant().toEpochMilli();
        long end = nextMonday.atStartOfDay(ZONE).toInstant().toEpochMilli() - 1;
        return new long[]{start, end};
    }

    /**
     * 获取一周内任意起止日（如周二到下周一）的时间戳范围
     *
     * @param dt          基准时间
     * @param startOffset 起始日相对本周一的偏移（0=周一，1=周二，-1=上周日等）
     * @return [start, end] 毫秒时间戳
     */
    public static LocalDate[] getWeekDateRange(LocalDateTime dt, int startOffset) {
        LocalDate baseMonday = dt.toLocalDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate startDay = baseMonday.plusDays(startOffset);
        LocalDate nextMonday = baseMonday.plusWeeks(1);
        return new LocalDate[]{startDay, nextMonday};
    }

    /**
     * 获取一月（当月1日00:00～下月1日00:00前1毫秒）的时间戳范围
     */
    public static long[] getMonthRange(LocalDateTime dt) {
        LocalDate firstOfMonth = dt.toLocalDate().withDayOfMonth(1);
        LocalDate firstOfNextMonth = firstOfMonth.plusMonths(1);
        long start = firstOfMonth.atStartOfDay(ZONE).toInstant().toEpochMilli();
        long end = firstOfNextMonth.atStartOfDay(ZONE).toInstant().toEpochMilli() - 1;
        return new long[]{start, end};
    }

    /**
     * 获取指定 LocalDateTime 在东八区的毫秒时间戳
     */
    public static long toEpochMilli(LocalDateTime dt) {
        return dt.atZone(ZONE).toInstant().toEpochMilli();
    }

    /**
     * 根据时间戳获取东八区的 LocalDateTime
     */
    public static LocalDateTime ofEpochMilli(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZONE);
    }

    /**
     * 解析任意常见格式，包括：
     * 20250101123045
     * 2025-5-12
     * 2025-05-12 9:5
     * 2025/5/1
     */
    public static LocalDateTime toLocalDateTime(String text) {
        if (text == null || text.isBlank()) return null;
        // 先把斜杠统一为横杠，兼容 “2025/5/1”
        String t = text.trim().replace('/', '-');
        return LocalDateTime.parse(t, FLEXIBLE_DATE_TIME_PARSER);
    }




}
