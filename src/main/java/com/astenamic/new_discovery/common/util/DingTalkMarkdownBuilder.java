package com.astenamic.new_discovery.common.util;


public class DingTalkMarkdownBuilder {

    private final StringBuilder sb = new StringBuilder();

    /**
     * 添加消息标题
     *
     * @param title 标题文本
     * @return 当前 DingTalkMarkdownBuilder 实例
     */
    public DingTalkMarkdownBuilder title(String title) {
        sb.append("#### ").append(title).append("\n\n");
        return this;
    }

    /**
     * 添加纯文本内容
     *
     * @param text 文本描述内容
     * @return 当前 DingTalkMarkdownBuilder 实例
     */
    public DingTalkMarkdownBuilder text(String text) {
        sb.append(text).append("\n\n");
        return this;
    }

    /**
     * 添加代码块，指定代码语言，使代码渲染更友好
     *
     * @param code 代码文本
     * @param language 代码语言，如 java、sql 等
     * @return 当前 DingTalkMarkdownBuilder 实例
     */
    public DingTalkMarkdownBuilder code(String code, String language) {
        sb.append("```").append(language).append("\n")
                .append(code).append("\n```").append("\n\n");
        return this;
    }

    /**
     * 构建最终的 Markdown 消息字符串
     *
     * @return 完整的 Markdown 格式消息字符串
     */
    public String build() {
        return sb.toString();
    }

    /**
     * 静态辅助方法，快速构建简单的 Markdown 消息
     *
     * @param title 标题文本
     * @param content 消息正文
     * @return Markdown 格式消息字符串
     */
    public static String buildSimpleMarkdown(String title, String content) {
        return new DingTalkMarkdownBuilder().title(title).text(content).build();
    }
}
