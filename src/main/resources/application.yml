spring:
  application:
    name: xfx
  datasource:
    url: *************************************************************************************
    username: xfxrpaymo
    password: Xfxrpaymo123?
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    properties:
      hibernate.format_sql: true
  # 文件日志输出的格式

scheduler:
  enabled: ${SCHEDULER_ENABLED:false}
  alert:
    monitor-keyword: monitor
    at-all: true
    system-name: 定时任务系统
    failure-alert-interval: 300

# 新增钉钉机器人配置
dingtalk:
  bots:
    default: # 默认机器人
      access-token: 7784bf63df8cd89963317192fec71a06902bc7ad756c16aae1ee0ee9298d4ba4
      secret: SEC32d5c7192ed1d89c27ac5ccd2348e1f0506c2055a95b5c3a3f5cd7d0dd750a86
    monitor: # 监控专用机器人
      access-token: b2849632838d6dfa0dbe1428ed5879558cef88b14683d0c91dc5b4c6d7915f77
      secret: SECe5001d536d70c6f9f28eb80272eefec101de74d060999c0b5facaedfa8c05bd5

elm:
  configs:
    xfx:
      sandbox: false
      app-key: 2zTgDMnwiu
      app-secret: 74bffd9dab9f62585a4d1bb654966a2a6510c5f7

mt:
  configs:
    xfx:
      appId: 425
      appSecret: 836b9f50cfca8a7fb1b96e69aabf3f0e

wlift:
  base-url: https://api.acewill.net/
  configs:
    xfx:
      appId: dp3Pg0HcYvLq7Hx9q5n1ae2
      appKey: ********************************
    kj:
      appId: dp0lGlNDH2m8MppgeE8Pq2jJ
      appKey: 519c1bd9ee47443220c0512464e80452
    hdl:
      appId: dp2zXvU6GNb3IXFBKUh4RzZQ
      appKey: c8a0caff10bc21549a4db42a453c770e

shuhai:
  app-key: SHSC2207225798
  app-secret: 150699083456544768
  base-url: https://opengateway.shuhaisc.com/

yida:
  config:
    appType: APP_SW1NM01KIYJPSUV9CFTN
    sysToken: S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K
    appKey: dingpuqekuek0vjz8fi4
    appSecret: uuD37xbiIrTuwZFX9Vjr70AD_4jIRSH7T7OPZTZABtJDm0Y_3qJn0DzNMY
    yida-bot:
      flow-bot: 570259207

# 统一的安全配置
security:
  modules:
    yida:
      token:
        keys: ${API_KEYS:5a5o-09f5-h092-d6ce-9a4a-608b-55c2-9d64}
        param-name: token
        allow-header-fallback: true
        exclude-paths: []
    scheduler:
      token:
        keys: ${SCHEDULER_API_KEYS:default-scheduler-token}
        param-name: token
        allow-header-fallback: true
        exclude-paths: []

aliyun:
  quick-bi:
    base-url: https://bi.aliyuncs.com/token3rd/dashboard/view/pc.htm?pageId=${pageId}&accessTicket=${accessTicket}
    access-key-id: LTAI5tBH7cvcX24NtHTuSm1i
    access-key-secret: ******************************
    region: cn-hangzhou
    endpoint: quickbi-public.cn-hangzhou.aliyuncs.com

cool:
  vision:
    base-url: https://gateway-api.coolstore.cn
    private-key: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCNVj5LaDKQZO5X9ilmZux3IPo8mvU3Daz/ckgVmqz6S7dAU9Qm/lvhUbLTqFuiSTUa0XqqF5tHiTEGgRHeq5AxC9g3mnlmL+t/2hvTKGGdAJ7N6WvIrOf0IIDOwE0YaQ14P7+7kjL4i1B1wcmXlXJkWSXef5/ch7vsqaMtgFWSme28EhO/OEmmbysopD7lmzQAb/6jJUqNYuRlZOnhv2Cd14FhVjQ/3BaSnrJ9uEd7buhG4RA4Yr8bYmuU4Tuc6DHG7p+m5sACRmQvi3Wzvk2NgWcPOb+U39z6RxBZDF79K9uDRH6s/N7sxrQipkxLNH61syZlLap7XHjGJMEZE67JAgMBAAECggEAZEyLsJ+lIms96RY7kvv/Wxr/+fpNgmAmILFNDmfR0V3iEbhrmbBwK5n6udGEFcq18O4vin1uKHnnXKJDuC0wCl5JdkwdUrM3AGgbzuGdQ/mouJ+paaOqh8h5ID+/dk82y9sRiV7a5R6YXrjDAtt/bk8FXLxdCfcZGX3Y2vVzxLB73DA2mNF53MhVIa7EIKVnJAIE/xkmtQRGfEpqPONoC6BMPZr4MZstBf7iAtvvYnkDjUHG/3637rYfU+WgN5piQ+Y7BLfLYA0EAk6w61r3bdtTazcPun9j9x56JcrxyWQ76ZtD2fWyt8qPHeB/Q0vv0NZy4o2LbRNHYxWCXs2osQKBgQDqsMleuorfQftST84vRbQv6ybaMN4wqX+IUtkFXy8hu10sho/wdGN86Slyzpl1bvsiurl3v3lTqSl6ibWQqlhOyV3ijDLu7h+fw+8CpZ85HBxwBkru1XtrPE09EmMNMG38JUJciX7xhWGsKfsrMq5lTodmsGgobSJFShjffSBKcwKBgQCaK4R97JyUTTvYcInvke9LuJWxQ/wTq1SIkS/6DBO69bguEidD1e7Xyhxc+uUIngf72knJpyO5zKNMRiD3bie7i1AOfNHkdIVVQbJROYTaOTvqvCpbbbvGaI5DA7MTYLCLbPDdClEvAiwTyt+t5nW+tTBwrdVk2H0+FYbohWHm0wKBgQC0ooE7QgTlNZnEjtw5kkLPpwRuZ7J8bdlVCb8z3UEfi2B5htB+2HZCjAt07AM9WcA9yM47lspBVDAyuo9e213AtkhmuFXzhKAejeYGeYeFo1dX+d/u1614qh8g5GgMnLvqmJrtMn7cUjcNFfrWEtob60dYCGsCwozgG9ntGheidQKBgQCOL9KLPN0QXcbzEJ/2xtY3NO9CRRxIm7xkE9t2twq/ZH5QZ0e1ZapZdLsB+hegAfgFDMb+51qospZs80TWTs7wRkU4h2AhXmHWRrgJ942SojSjgpViK2uCSs3h+ph/J67LeFcQ1cIYI925B2OShDlWAM/DLF/iGVQxHTOGYfXwJwKBgAdlO3PMwWmZZNzSbmMOMJbAz9MB+WXcrrkWQEzFpbQ/JGLVv3CngjRl+G+ldNXOGfowEoPoxhW17jpatTdVKgCTkEyVWTwZZCxT+13gQrG73INnovFbj/HMkaccrEH8gQzQ6fVUIYhhxO8x3baSfcu90f9Ia6XXER/2FZbaGjQj
    enterprise-id: f90a36df7ad24961865e1a3fed96e8f3
  college:
    base-url: https://open-service.coolcollege.cn
    api-key: 4db479362b3d4cf2b2f870e744bbf114
    api-secret: 24ebe47d898c4304a0e9543e35e057ce
    enterprise-id: 1777152000694292492
    connect-timeout-ms: 3000
    read-timeout-ms: 5000
