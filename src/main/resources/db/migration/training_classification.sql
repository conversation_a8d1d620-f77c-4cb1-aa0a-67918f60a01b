-- 学习项目培训分类表
CREATE TABLE IF NOT EXISTS xfx_cool_training_classification (
    -- 主键
    id BIGSERIAL PRIMARY KEY,
    
    -- 删除标识：0-正常 1-删除
    del_flag VARCHAR(1) DEFAULT '0',
    
    -- 创建时间
    create_time TIMESTAMP WITHOUT TIME ZONE,
    
    -- 更新时间
    update_time TIMESTAMP WITHOUT TIME ZONE,
    
    -- 分类路径（不含自身，逗号分隔）
    parent_id_chain TEXT,
    
    -- 级别：1→一级 2→二级 3→三级
    level INTEGER,
    
    -- 描述
    description TEXT,
    
    -- 排序
    sort INTEGER,
    
    -- 类型名称
    title VARCHAR(128),
    
    -- 类型：0 系统预制 1 业务
    type INTEGER,
    
    -- 是否启用
    enabled BOOLEAN,
    
    -- 父节点是否启用
    parent_enable BOOLEAN,
    
    -- 更新人 ID
    update_user BIGINT,
    
    -- 父级 ID（无父节点为 0）
    parent_id BIGINT,
    
    -- 分类名
    name VARCHAR(128),
    
    -- 是否禁用
    disabled BOOLEAN,
    
    -- 创建人 ID
    create_user BIGINT,
    
    -- 分类 key（与 id 一致）
    key_value BIGINT,

    -- 业务ID（来自外部系统的原始ID）
    biz_id BIGINT,

    -- 业务创建时间
    biz_create_time TIMESTAMP WITHOUT TIME ZONE,

    -- 业务更新时间
    biz_update_time TIMESTAMP WITHOUT TIME ZONE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_training_classification_parent_id ON xfx_cool_training_classification(parent_id);
CREATE INDEX IF NOT EXISTS idx_training_classification_level ON xfx_cool_training_classification(level);
CREATE INDEX IF NOT EXISTS idx_training_classification_enabled ON xfx_cool_training_classification(enabled);
CREATE INDEX IF NOT EXISTS idx_training_classification_type ON xfx_cool_training_classification(type);
CREATE INDEX IF NOT EXISTS idx_training_classification_del_flag ON xfx_cool_training_classification(del_flag);
CREATE INDEX IF NOT EXISTS idx_training_classification_name ON xfx_cool_training_classification(name);
CREATE INDEX IF NOT EXISTS idx_training_classification_biz_id ON xfx_cool_training_classification(biz_id);

-- 添加表注释
COMMENT ON TABLE xfx_cool_training_classification IS '学习项目培训分类表';

-- 添加字段注释
COMMENT ON COLUMN xfx_cool_training_classification.id IS '主键';
COMMENT ON COLUMN xfx_cool_training_classification.del_flag IS '删除标识：0-正常 1-删除';
COMMENT ON COLUMN xfx_cool_training_classification.create_time IS '创建时间';
COMMENT ON COLUMN xfx_cool_training_classification.update_time IS '更新时间';
COMMENT ON COLUMN xfx_cool_training_classification.parent_id_chain IS '分类路径（不含自身，逗号分隔）';
COMMENT ON COLUMN xfx_cool_training_classification.level IS '级别：1→一级 2→二级 3→三级';
COMMENT ON COLUMN xfx_cool_training_classification.description IS '描述';
COMMENT ON COLUMN xfx_cool_training_classification.sort IS '排序';
COMMENT ON COLUMN xfx_cool_training_classification.title IS '类型名称';
COMMENT ON COLUMN xfx_cool_training_classification.type IS '类型：0 系统预制 1 业务';
COMMENT ON COLUMN xfx_cool_training_classification.enabled IS '是否启用';
COMMENT ON COLUMN xfx_cool_training_classification.parent_enable IS '父节点是否启用';
COMMENT ON COLUMN xfx_cool_training_classification.update_user IS '更新人 ID';
COMMENT ON COLUMN xfx_cool_training_classification.parent_id IS '父级 ID（无父节点为 0）';
COMMENT ON COLUMN xfx_cool_training_classification.name IS '分类名';
COMMENT ON COLUMN xfx_cool_training_classification.disabled IS '是否禁用';
COMMENT ON COLUMN xfx_cool_training_classification.create_user IS '创建人 ID';
COMMENT ON COLUMN xfx_cool_training_classification.key_value IS '分类 key（与 id 一致）';
COMMENT ON COLUMN xfx_cool_training_classification.biz_id IS '业务ID（来自外部系统的原始ID）';
COMMENT ON COLUMN xfx_cool_training_classification.biz_create_time IS '业务创建时间';
COMMENT ON COLUMN xfx_cool_training_classification.biz_update_time IS '业务更新时间';
